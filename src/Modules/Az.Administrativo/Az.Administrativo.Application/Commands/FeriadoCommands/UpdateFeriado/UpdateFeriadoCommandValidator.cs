using FluentValidation;

namespace Az.Administrativo.Application.Commands.FeriadoCommands;

public class UpdateFeriadoCommandValidator : AbstractValidator<UpdateFeriadoCommand>
{
    public UpdateFeriadoCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEqual(Guid.Empty).WithMessage("Id é obrigatório");
        RuleFor(x => x.Data)
            .NotEqual(default(DateOnly)).WithMessage("Data é obrigatória");
        RuleFor(x => x.Descricao)
            .NotEmpty().WithMessage("Descrição é obrigatória");
    }
}