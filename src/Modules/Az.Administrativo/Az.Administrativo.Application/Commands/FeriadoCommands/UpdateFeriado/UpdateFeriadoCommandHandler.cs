using MediatR;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.IRepositories;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Administrativo.Application.Services.Interfaces;

namespace Az.Administrativo.Application.Commands.FeriadoCommands;

public class UpdateFeriadoCommandHandler : IRequestHandler<UpdateFeriadoCommand, OperationResult<bool>>
{
    private readonly IFeriadoRepository _feriadoRepository;
    private readonly IUnitOfWork<AdministrativoDbContext> _unitOfWork;
    private readonly ICacheInvalidationService _cacheInvalidationService;

    public UpdateFeriadoCommandHandler(
        IFeriadoRepository feriadoRepository,
        IUnitOfWork<AdministrativoDbContext> unitOfWork,
        ICacheInvalidationService cacheInvalidationService)
    {
        _feriadoRepository = feriadoRepository;
        _unitOfWork = unitOfWork;
        _cacheInvalidationService = cacheInvalidationService;
    }

    public async Task<OperationResult<bool>> Handle(UpdateFeriadoCommand request, CancellationToken cancellationToken)
    {
        var feriado = await _feriadoRepository.GetByIdAsync(request.Id);
        if (feriado == null)
            return OperationResult.Error<bool>(["Feriado não encontrado"]);

        feriado.AlterarData(request.Data);
        feriado.AlterarDescricao(request.Descricao);
        await _feriadoRepository.UpdateAsync(feriado);
        await _unitOfWork.CommitAsync();

        // Invalidar cache de feriados e datas disponíveis
        await _cacheInvalidationService.InvalidateFeriadosAsync(cancellationToken);

        return OperationResult.Result(true);
    }
}