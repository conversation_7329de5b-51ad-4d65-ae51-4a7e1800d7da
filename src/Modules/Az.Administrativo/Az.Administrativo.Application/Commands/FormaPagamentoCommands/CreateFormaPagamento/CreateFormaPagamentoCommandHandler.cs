using MediatR;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.IRepositories;

namespace Az.Administrativo.Application.Commands.FormaPagamentoCommands.CreateFormaPagamento;

public class CreateFormaPagamentoCommandHandler : IRequestHandler<CreateFormaPagamentoCommand, OperationResult<Guid>>
{
    private readonly IFormaPagamentoRepository _repository;
    private readonly IUnitOfWork<AdministrativoDbContext> _unitOfWork;

    public CreateFormaPagamentoCommandHandler(IFormaPagamentoRepository repository, 
        IUnitOfWork<AdministrativoDbContext> unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<OperationResult<Guid>> Handle(CreateFormaPagamentoCommand request, CancellationToken cancellationToken)
    {
        var formaPagamento = new FormaPagamento(
            request.Nome,
            request.Tipo,
            request.MaximoParcelas,
            request.Observacao,
            request.Acrescimo
        );
        await _repository.AddAsync(formaPagamento);
        await _unitOfWork.CommitAsync();
        return OperationResult.Result(formaPagamento.IdFormaPagamento);
    }
}