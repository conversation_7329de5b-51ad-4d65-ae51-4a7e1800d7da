using MediatR;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.IRepositories;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.Services.Interfaces;
using Az.Administrativo.Application.Services.Interfaces;

namespace Az.Administrativo.Application.Commands.HorarioFuncionamentoCommands.UpdateHorarioFuncionamento;

public class UpdateHorarioFuncionamentoCommandHandler : IRequestHandler<UpdateHorarioFuncionamentoCommand, OperationResult<bool>>
{
    private readonly IHorarioFuncionamentoRepository _repository;
    private readonly IUnitOfWork<AdministrativoDbContext> _unitOfWork;
    private readonly ICurrentUser _currentUser;
    private readonly ICacheInvalidationService _cacheInvalidationService;

    public UpdateHorarioFuncionamentoCommandHandler(
        IHorarioFuncionamentoRepository repository,
        IUnitOfWork<AdministrativoDbContext> unitOfWork,
        ICurrentUser currentUser,
        ICacheInvalidationService cacheInvalidationService)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _currentUser = currentUser;
        _cacheInvalidationService = cacheInvalidationService;
    }

    public async Task<OperationResult<bool>> Handle(UpdateHorarioFuncionamentoCommand request, CancellationToken cancellationToken)
    {
        var horario = await _repository.GetByIdAsync(request.Id);
        if (horario == null)
            return OperationResult.Error<bool>(["Horário de funcionamento não encontrado"]);

        horario.AlterarHoraAbertura(request.HoraAbertura);
        horario.AlterarHoraFechamento(request.HoraFechamento);
        horario.AtualizarAlteracao(Guid.Parse(_currentUser.UserId!));

        await _repository.UpdateAsync(horario);
        await _unitOfWork.CommitAsync();

        // Invalidar cache de horários de funcionamento e datas disponíveis
        await _cacheInvalidationService.InvalidateHorariosFuncionamentoAsync(cancellationToken);

        return OperationResult.Result(true);
    }
}