using FluentValidation;

namespace Az.Administrativo.Application.Commands.HorarioFuncionamentoCommands.UpdateHorarioFuncionamento;

public class UpdateHorarioFuncionamentoCommandValidator : AbstractValidator<UpdateHorarioFuncionamentoCommand>
{
    public UpdateHorarioFuncionamentoCommandValidator()
    {
        RuleFor(x => x.HoraAbertura)
            .NotNull().WithMessage("Hora de abertura é obrigatória");
        RuleFor(x => x.HoraFechamento)
            .NotNull().WithMessage("Hora de fechamento é obrigatória");
        RuleFor(x => x)
            .Must(x => x.HoraAbertura < x.HoraFechamento)
            .WithMessage("Hora de abertura deve ser menor que a de fechamento");
        RuleFor(x => x)
            .Must(x => x.HoraAbertura != x.HoraFechamento)
            .WithMessage("Hora de abertura e fechamento não podem ser iguais");
    }
}