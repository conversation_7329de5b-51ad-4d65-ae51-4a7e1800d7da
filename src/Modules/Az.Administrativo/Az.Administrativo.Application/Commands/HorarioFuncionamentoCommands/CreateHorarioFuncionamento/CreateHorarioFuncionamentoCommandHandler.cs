using MediatR;
using Az.Shared.Shared.Common;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Administrativo.Domain.Entities;
using Az.Shared.Shared.IRepositories;
using Az.Administrativo.Application.Services.Interfaces;

namespace Az.Administrativo.Application.Commands.HorarioFuncionamentoCommands.CreateHorarioFuncionamento;

public class CreateHorarioFuncionamentoCommandHandler : IRequestHandler<CreateHorarioFuncionamentoCommand, OperationResult<Guid>>
{
    private readonly IHorarioFuncionamentoRepository _repository;
    private readonly IUnitOfWork<AdministrativoDbContext> _unitOfWork;
    private readonly ICacheInvalidationService _cacheInvalidationService;

    public CreateHorarioFuncionamentoCommandHandler(
        IHorarioFuncionamentoRepository repository,
        IUnitOfWork<AdministrativoDbContext> unitOfWork,
        ICacheInvalidationService cacheInvalidationService)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _cacheInvalidationService = cacheInvalidationService;
    }

    public async Task<OperationResult<Guid>> Handle(CreateHorarioFuncionamentoCommand request, CancellationToken cancellationToken)
    {
        var horario = new HorarioFuncionamento(request.DiaSemana, request.HoraAbertura, request.HoraFechamento);
        await _repository.AddAsync(horario);
        await _unitOfWork.CommitAsync();

        // Invalidar cache de horários de funcionamento e datas disponíveis
        await _cacheInvalidationService.InvalidateHorariosFuncionamentoAsync(cancellationToken);

        return OperationResult.Result(horario.IdHorarioFuncionamento);
    }
}