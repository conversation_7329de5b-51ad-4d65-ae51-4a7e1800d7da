using FluentValidation;

namespace Az.Administrativo.Application.Commands.TermoCondicaoCommands.CreateTermoCondicao;

public class CreateTermoCondicaoCommandValidator : AbstractValidator<CreateTermoCondicaoCommand>
{
    public CreateTermoCondicaoCommandValidator()
    {
        RuleFor(x => x.Titulo)
            .NotEmpty().WithMessage("Título não pode ser vazio.")
            .NotNull().WithMessage("O título é obrigatório.")
            .MaximumLength(200).WithMessage("O título não pode ter mais de 200 caracteres.")
            .MinimumLength(3).WithMessage("O título deve ter pelo menos 3 caracteres.");

        RuleFor(x => x.Descricao)
            .NotEmpty().WithMessage("Descrição não pode ser vazia.")
            .NotNull().WithMessage("A descrição é obrigatória.");
        
        RuleFor(x => x.Descricao)
            .MaximumLength(5000).WithMessage("Descrição não pode exceder 5000 caracteres.");
    }
}
