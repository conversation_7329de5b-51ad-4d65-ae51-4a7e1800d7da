using FluentValidation;

namespace Az.Administrativo.Application.Commands.TermoCondicaoCommands.UpdateTermoCondicao;

public class UpdateTermoCondicaoCommandValidator : AbstractValidator<UpdateTermoCondicaoCommand>
{
    public UpdateTermoCondicaoCommandValidator()
    {
        RuleFor(x => x.IdTermoCondicao)
            .NotEmpty().WithMessage("O ID do Termo e Condição é obrigatório.");

        RuleFor(x => x.Titulo)
            .NotEmpty().WithMessage("O título é obrigatório.")
            .MaximumLength(200).WithMessage("O título não pode ter mais de 200 caracteres.");

        RuleFor(x => x.Descricao)
            .NotEmpty().WithMessage("A descrição é obrigatória.");
    }
}
