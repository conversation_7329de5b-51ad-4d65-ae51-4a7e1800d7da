using MediatR;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;

namespace Az.Administrativo.Application.Commands.TermoCondicaoCommands.UpdateTermoCondicao;

public class UpdateTermoCondicaoCommandHandler : IRequestHandler<UpdateTermoCondicaoCommand, OperationResult<bool>>
{
    private readonly ITermoCondicaoRepository _termoCondicaoRepository;
    private readonly IUnitOfWork<AdministrativoDbContext> _unitOfWork;
    private readonly ICurrentUser _currentUser;

    public UpdateTermoCondicaoCommandHandler(ITermoCondicaoRepository termoCondicaoRepository, IUnitOfWork<AdministrativoDbContext> unitOfWork, ICurrentUser currentUser)
    {
        _termoCondicaoRepository = termoCondicaoRepository;
        _unitOfWork = unitOfWork;
        _currentUser = currentUser;
    }

    public async Task<OperationResult<bool>> Handle(UpdateTermoCondicaoCommand request, CancellationToken cancellationToken)
    {
        var termoCondicao = await _termoCondicaoRepository.GetByIdAsync(request.IdTermoCondicao);

        if (termoCondicao == null)
            return OperationResult.Error<bool>(["Termo e Condição não encontrado."]);

        termoCondicao.AlterarDescricao(request.Descricao);
        termoCondicao.AlterarTitulo(request.Titulo);
        termoCondicao.AtualizarAlteracao(Guid.Parse(_currentUser.UserId!));

        await _termoCondicaoRepository.UpdateAsync(termoCondicao);
        await _unitOfWork.CommitAsync();

        return OperationResult.Result(true);
    }
}
