using MediatR;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;

namespace Az.Administrativo.Application.Commands.TipoPacoteCommands.DeleteTipoPacote;

public class DeleteTipoPacoteCommandHandler : IRequestHandler<DeleteTipoPacoteCommand, OperationResult<bool>>
{
    private readonly ITipoPacoteRepository _repository;
    private readonly IUnitOfWork<AdministrativoDbContext> _unitOfWork;
    private readonly ICurrentUser _currentUser;

    public DeleteTipoPacoteCommandHandler(
        ITipoPacoteRepository repository,
        IUnitOfWork<AdministrativoDbContext> unitOfWork,
        ICurrentUser currentUser)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _currentUser = currentUser;
    }

    public async Task<OperationResult<bool>> Handle(DeleteTipoPacoteCommand request, CancellationToken cancellationToken)
    {
        var tipoPacote = await _repository.GetByIdAsync(request.IdTipoPacote);
        
        if (tipoPacote == null)
            return OperationResult.Error<bool>(["Tipo de pacote não encontrado"]);

        await _repository.DeleteAsync(tipoPacote, Guid.Parse(_currentUser.UserId!));
        await _unitOfWork.CommitAsync();

        return OperationResult.Result(true);
    }
}
