using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Az.Administrativo.Domain.IRepositories;
using Az.Shared.Shared.Common;

namespace Az.Administrativo.Application.Queries.TermoCondicaoQueries.GetTermoCondicaoById;

public class GetTermoCondicaoByIdQueryHandler : IRequestHandler<GetTermoCondicaoByIdQuery, OperationResult<GetTermoCondicaoByIdResponse>>
{
    private readonly ITermoCondicaoRepository _termoCondicaoRepository;

    public GetTermoCondicaoByIdQueryHandler(ITermoCondicaoRepository termoCondicaoRepository)
    {
        _termoCondicaoRepository = termoCondicaoRepository;
    }

    public async Task<OperationResult<GetTermoCondicaoByIdResponse>> Handle(GetTermoCondicaoByIdQuery request, CancellationToken cancellationToken)
    {
        var termoCondicao = await _termoCondicaoRepository.GetByIdAsync(request.IdTermoCondicao);

        if (termoCondicao == null)
            return OperationResult.Error<GetTermoCondicaoByIdResponse>([ "Termo e Condição não encontrado." ]);

        var response = new GetTermoCondicaoByIdResponse
        {
            IdTermoCondicao = termoCondicao.IdTermoCondicao,
            Titulo = termoCondicao.Titulo,
            Descricao = termoCondicao.Descricao,
            DataDeCriacao = termoCondicao.DataDeCriacao,
            Ativo = termoCondicao.Ativo
        };

        return OperationResult.Result(response);
    }
}
