using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Az.Administrativo.Domain.IRepositories;
using Az.Shared.Shared.Common;

namespace Az.Administrativo.Application.Queries.TermoCondicaoQueries.GetAllTermosCondicoes;

public class GetAllTermosCondicoesQueryHandler : IRequestHandler<GetAllTermosCondicoesQuery, OperationResult<List<GetAllTermosCondicoesResponse>>>
{
    private readonly ITermoCondicaoRepository _termoCondicaoRepository;

    public GetAllTermosCondicoesQueryHandler(ITermoCondicaoRepository termoCondicaoRepository)
    {
        _termoCondicaoRepository = termoCondicaoRepository;
    }

    public async Task<OperationResult<List<GetAllTermosCondicoesResponse>>> Handle(GetAllTermosCondicoesQuery request, CancellationToken cancellationToken)
    {
        var termosCondicoes = await _termoCondicaoRepository.GetAllAsync();

        var response = termosCondicoes.Select(tc => new GetAllTermosCondicoesResponse
        {
            IdTermoCondicao = tc.IdTermoCondicao,
            Titulo = tc.Titulo,
            Descricao = tc.Descricao,
            DataDeCriacao = tc.DataDeCriacao
        }).ToList();

        return OperationResult.Result(response);
    }
}
