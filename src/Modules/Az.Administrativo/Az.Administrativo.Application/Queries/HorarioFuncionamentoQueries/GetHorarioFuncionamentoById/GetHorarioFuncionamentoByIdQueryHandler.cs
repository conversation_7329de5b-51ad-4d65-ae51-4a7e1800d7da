using MediatR;
using Az.Shared.Shared.Common;
using Az.Administrativo.Domain.IRepositories;

namespace Az.Administrativo.Application.Queries.HorarioFuncionamentoQueries.GetHorarioFuncionamentoById;

public class GetHorarioFuncionamentoByIdQueryHandler : IRequestHandler<GetHorarioFuncionamentoByIdQuery, OperationResult<GetHorarioFuncionamentoByIdResponse>>
{
    private readonly IHorarioFuncionamentoRepository _repository;

    public GetHorarioFuncionamentoByIdQueryHandler(IHorarioFuncionamentoRepository repository)
    {
        _repository = repository;
    }

    public async Task<OperationResult<GetHorarioFuncionamentoByIdResponse>> Handle(GetHorarioFuncionamentoByIdQuery request, CancellationToken cancellationToken)
    {
        var horario = await _repository.GetByIdAsync(request.Id);
        if (horario == null)
            return OperationResult.Error<GetHorarioFuncionamentoByIdResponse>(["Horário de funcionamento não encontrado"]);

        var response = new GetHorarioFuncionamentoByIdResponse(
            horario.IdHorarioFuncionamento,
            horario.DiaSemana,
            horario.HoraAbertura,
            horario.HoraFechamento
        );
        return OperationResult.Result(response);
    }
}