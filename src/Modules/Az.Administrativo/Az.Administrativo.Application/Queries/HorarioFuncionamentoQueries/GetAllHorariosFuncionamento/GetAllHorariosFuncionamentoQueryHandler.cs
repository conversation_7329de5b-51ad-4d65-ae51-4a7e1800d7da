using MediatR;
using Az.Shared.Shared.Common;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Application.Queries.HorarioFuncionamentoQueries.GetHorarioFuncionamentoById;
using Az.Administrativo.Application.Queries.HorarioFuncionamentoQueries.GetAllHorariosFuncionamento;

namespace Az.Administrativo.Application.Queries.HorarioFuncionamentoQueries.GetAllHorariosFuncionamento;

public class GetAllHorariosFuncionamentoQueryHandler : IRequestHandler<GetAllHorariosFuncionamentoQuery, OperationResult<List<GetAllHorariosFuncionamentoResponse>>>
{
    private readonly IHorarioFuncionamentoRepository _repository;

    public GetAllHorariosFuncionamentoQueryHandler(IHorarioFuncionamentoRepository repository)
    {
        _repository = repository;
    }

    public async Task<OperationResult<List<GetAllHorariosFuncionamentoResponse>>> Handle(GetAllHorariosFuncionamentoQuery request, CancellationToken cancellationToken)
    {
        var horarios = await _repository.GetAllAsync();
        var responses = horarios.Select(h => new GetAllHorariosFuncionamentoResponse(
            h.IdHorarioFuncionamento,
            h.DiaSemana,
            h.HoraAbertura,
            h.HoraFechamento
        )).ToList();
        return OperationResult.Result(responses);
    }
}