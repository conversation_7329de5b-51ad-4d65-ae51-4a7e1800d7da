using MediatR;
using Az.Administrativo.Domain.IRepositories;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.Services.Interfaces;
using Az.Shared.Shared.Services;
using Microsoft.Extensions.Logging;

namespace Az.Administrativo.Application.Queries.HorarioFuncionamentoQueries.GetDatasDisponiveisParaInicio;

/// <summary>
/// Handler responsável por calcular as próximas datas disponíveis para início de contrato
/// Implementa cache Redis para otimização de performance
/// </summary>
public class GetDatasDisponiveisParaInicioQueryHandler : IRequestHandler<GetDatasDisponiveisParaInicioQuery, OperationResult<List<DateOnly>>>
{
    #region Constants

    private const int QUANTIDADE_DATAS = 20;
    private const int MULTIPLICADOR_RANGE_FERIADOS = 3;
    private const int LIMITE_PROTECAO_LOOP = 365;

    #endregion

    #region Dependencies

    private readonly IFeriadoRepository _feriadoRepository;
    private readonly IHorarioFuncionamentoRepository _horarioRepository;
    private readonly ICacheService _cacheService;
    private readonly ILogger<GetDatasDisponiveisParaInicioQueryHandler> _logger;

    #endregion
 
    #region Constructor

    public GetDatasDisponiveisParaInicioQueryHandler(
        IFeriadoRepository feriadoRepository,
        IHorarioFuncionamentoRepository horarioRepository,
        ICacheService cacheService,
        ILogger<GetDatasDisponiveisParaInicioQueryHandler> logger)
    {
        _feriadoRepository = feriadoRepository;
        _horarioRepository = horarioRepository;
        _cacheService = cacheService;
        _logger = logger;
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Processa a query para obter as próximas datas disponíveis para início de contrato
    /// </summary>
    /// <param name="request">Query de entrada</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista com as próximas 20 datas disponíveis</returns>
    public async Task<OperationResult<List<DateOnly>>> Handle(GetDatasDisponiveisParaInicioQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Iniciando cálculo de datas disponíveis para início");

        // 1. Gerar chave de cache baseada na data atual (para considerar mudanças de dia)
        var hoje = DateOnly.FromDateTime(DateTime.Now);
        var cacheKey = CacheKeys.Administrativo.DatasDisponiveis.ParaInicioComData(hoje);

        // 2. Verificar cache PRIMEIRO
        var datasCache = await TentarObterDoCache(cacheKey);
        if (datasCache != null)
        {
            return OperationResult.Result(datasCache);
        }

        // 3. Cache miss - determinar data de início baseada nas regras de negócio
        var dataInicio = await DeterminarDataInicioAsync();

        // 4. Calcular datas disponíveis
        var datasDisponiveis = await CalcularDatasDisponiveisAsync(dataInicio);

        // 5. Armazenar no cache
        await ArmazenarNoCache(cacheKey, datasDisponiveis);

        _logger.LogInformation("Cálculo de datas disponíveis concluído. Total: {Total} datas", datasDisponiveis.Count);
        return OperationResult.Result(datasDisponiveis);
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// Determina a data de início baseada nas regras de negócio
    /// </summary>
    /// <returns>Data de início para o cálculo</returns>
    private async Task<DateOnly> DeterminarDataInicioAsync()
    {
        var agora = DateTime.Now;
        var hoje = DateOnly.FromDateTime(agora);
        var horaAtual = TimeOnly.FromDateTime(agora);

        _logger.LogDebug("Determinando data de início. Hoje: {Hoje}, Hora atual: {HoraAtual}", hoje, horaAtual);

        // Buscar horário de funcionamento para hoje
        var horarios = await _horarioRepository.GetAllAsync();
        var horarioHoje = horarios.FirstOrDefault(h => h.DiaSemana == hoje.DayOfWeek);

        if (horarioHoje == null)
        {
            _logger.LogDebug("Hoje ({DiaSemana}) não tem expediente cadastrado. Iniciando do próximo dia", hoje.DayOfWeek);
            return hoje.AddDays(1);
        }

        // Verificar se estamos dentro do horário de funcionamento
        if (horaAtual >= horarioHoje.HoraAbertura && horaAtual <= horarioHoje.HoraFechamento)
        {
            _logger.LogDebug("Dentro do expediente ({HoraAbertura}-{HoraFechamento}). Incluindo hoje",
                horarioHoje.HoraAbertura, horarioHoje.HoraFechamento);
            return hoje;
        }

        _logger.LogDebug("Fora do expediente ({HoraAbertura}-{HoraFechamento}). Iniciando do próximo dia",
            horarioHoje.HoraAbertura, horarioHoje.HoraFechamento);
        return hoje.AddDays(1);
    }

    /// <summary>
    /// Tenta obter as datas do cache Redis
    /// </summary>
    /// <param name="cacheKey">Chave do cache</param>
    /// <returns>Lista de datas ou null se não encontrado</returns>
    private async Task<List<DateOnly>?> TentarObterDoCache(string cacheKey)
    {
        try
        {
            _logger.LogDebug("Verificando cache Redis. Chave: {CacheKey}", cacheKey);

            var cachedResponse = await _cacheService.GetAsync<List<DateOnly>>(cacheKey);
            if (cachedResponse != null)
            {
                _logger.LogInformation(" Cache HIT - Datas obtidas do Redis. Total: {Total} datas", cachedResponse.Count);
                return cachedResponse;
            }

            _logger.LogInformation("Cache MISS - Dados não encontrados no cache. Calculando...");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao acessar cache Redis. Continuando sem cache. Chave: {CacheKey}", cacheKey);
            return null;
        }
    }

    /// <summary>
    /// Calcula as datas disponíveis baseado nas regras de negócio
    /// </summary>
    /// <param name="dataInicio">Data de início para o cálculo</param>
    /// <returns>Lista com as datas disponíveis</returns>
    private async Task<List<DateOnly>> CalcularDatasDisponiveisAsync(DateOnly dataInicio)
    {
        _logger.LogDebug("Iniciando cálculo de datas disponíveis a partir de: {DataInicio}", dataInicio);

        // Buscar horários de funcionamento
        var horarios = await _horarioRepository.GetAllAsync();
        var diasAbertos = new HashSet<DayOfWeek>(horarios.Select(h => h.DiaSemana));

        // Se não há dias abertos, retornar próximos dias corridos
        if (diasAbertos.Count == 0)
        {
            _logger.LogWarning("Nenhum horário de funcionamento cadastrado. Retornando próximos {Quantidade} dias corridos", QUANTIDADE_DATAS);
            return GerarDiasCorreidos(dataInicio);
        }

        // Buscar feriados para o período
        var feriados = await BuscarFeriadosAsync(dataInicio);

        // Calcular datas válidas
        return await CalcularDatasValidas(dataInicio, diasAbertos, feriados);
    }

    /// <summary>
    /// Gera uma lista com os próximos dias corridos (fallback quando não há expediente)
    /// </summary>
    /// <param name="dataInicio">Data de início</param>
    /// <returns>Lista com os próximos dias corridos</returns>
    private List<DateOnly> GerarDiasCorreidos(DateOnly dataInicio)
    {
        var datas = new List<DateOnly>();
        for (int i = 0; i < QUANTIDADE_DATAS; i++)
        {
            datas.Add(dataInicio.AddDays(i));
        }
        return datas;
    }

    /// <summary>
    /// Busca feriados para o período necessário
    /// </summary>
    /// <param name="dataInicio">Data de início</param>
    /// <returns>HashSet com as datas de feriados</returns>
    private async Task<HashSet<DateOnly>> BuscarFeriadosAsync(DateOnly dataInicio)
    {
        // Buscar feriados para um período maior para garantir que temos dados suficientes
        var dataFim = dataInicio.AddDays(QUANTIDADE_DATAS * MULTIPLICADOR_RANGE_FERIADOS);
        var feriados = await _feriadoRepository.GetByDateRangeAsync(dataInicio, dataFim);

        var feriadosSet = new HashSet<DateOnly>(feriados.Select(f => f.Data));
        _logger.LogDebug("Feriados encontrados no período {DataInicio} a {DataFim}: {Total}",
            dataInicio, dataFim, feriadosSet.Count);

        return feriadosSet;
    }

    /// <summary>
    /// Calcula as datas válidas baseado nos dias abertos e feriados
    /// </summary>
    /// <param name="dataInicio">Data de início</param>
    /// <param name="diasAbertos">Dias da semana com expediente</param>
    /// <param name="feriados">Datas de feriados</param>
    /// <returns>Lista com as datas válidas</returns>
    private async Task<List<DateOnly>> CalcularDatasValidas(DateOnly dataInicio, HashSet<DayOfWeek> diasAbertos, HashSet<DateOnly> feriados)
    {
        var datasDisponiveis = new List<DateOnly>();
        var diasAvancados = 0;
        var feriadosExpandidos = new HashSet<DateOnly>(feriados);

        _logger.LogDebug("Calculando datas válidas. Dias abertos: {DiasAbertos}",
            string.Join(", ", diasAbertos.Select(d => d.ToString())));

        while (datasDisponiveis.Count < QUANTIDADE_DATAS && diasAvancados < LIMITE_PROTECAO_LOOP)
        {
            var dataAtual = dataInicio.AddDays(diasAvancados);

            // Verificar se a data é válida (não é feriado e tem expediente)
            if (EhDataValida(dataAtual, diasAbertos, feriadosExpandidos))
            {
                datasDisponiveis.Add(dataAtual);
            }

            diasAvancados++;

            // Expandir busca de feriados se necessário
            if (diasAvancados % (QUANTIDADE_DATAS * MULTIPLICADOR_RANGE_FERIADOS) == 0)
            {
                await ExpandirBuscaFeriadosAsync(dataInicio, diasAvancados, feriadosExpandidos);
            }
        }

        if (diasAvancados >= LIMITE_PROTECAO_LOOP)
        {
            _logger.LogWarning("Atingido limite de proteção contra loop infinito. Retornando {Total} datas encontradas",
                datasDisponiveis.Count);
        }

        return datasDisponiveis;
    }

    /// <summary>
    /// Verifica se uma data é válida (não é feriado e tem expediente)
    /// </summary>
    /// <param name="data">Data a verificar</param>
    /// <param name="diasAbertos">Dias da semana com expediente</param>
    /// <param name="feriados">Datas de feriados</param>
    /// <returns>True se a data é válida</returns>
    private static bool EhDataValida(DateOnly data, HashSet<DayOfWeek> diasAbertos, HashSet<DateOnly> feriados)
    {
        return !feriados.Contains(data) && diasAbertos.Contains(data.DayOfWeek);
    }

    /// <summary>
    /// Expande a busca de feriados quando necessário
    /// </summary>
    /// <param name="dataInicio">Data de início original</param>
    /// <param name="diasAvancados">Quantidade de dias já avançados</param>
    /// <param name="feriadosExpandidos">HashSet de feriados para expandir</param>
    private async Task ExpandirBuscaFeriadosAsync(DateOnly dataInicio, int diasAvancados, HashSet<DateOnly> feriadosExpandidos)
    {
        var novaDataInicio = dataInicio.AddDays(diasAvancados);
        var novaDataFim = novaDataInicio.AddDays(QUANTIDADE_DATAS * MULTIPLICADOR_RANGE_FERIADOS);

        var novosFeriados = await _feriadoRepository.GetByDateRangeAsync(novaDataInicio, novaDataFim);

        var adicionados = 0;
        foreach (var feriado in novosFeriados)
        {
            if (feriadosExpandidos.Add(feriado.Data))
            {
                adicionados++;
            }
        }

        _logger.LogDebug("Expandida busca de feriados. Período: {DataInicio} a {DataFim}, Novos feriados: {Adicionados}",
            novaDataInicio, novaDataFim, adicionados);
    }

    /// <summary>
    /// Armazena as datas no cache Redis
    /// </summary>
    /// <param name="cacheKey">Chave do cache</param>
    /// <param name="datasDisponiveis">Datas para armazenar</param>
    private async Task ArmazenarNoCache(string cacheKey, List<DateOnly> datasDisponiveis)
    {
        try
        {
            await _cacheService.SetAsync(cacheKey, datasDisponiveis, CacheKeys.Expiration.OneDay);
            _logger.LogInformation("💾 Datas armazenadas no cache Redis. Total: {Total} datas, TTL: {TTL}",
                datasDisponiveis.Count, CacheKeys.Expiration.OneDay);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ Erro ao armazenar no cache Redis. Continuando sem cache. Chave: {CacheKey}", cacheKey);
        }
    }

    #endregion
}