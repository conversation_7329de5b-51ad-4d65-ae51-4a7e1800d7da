using MediatR;
using Az.Shared.Shared.Common;
using Az.Administrativo.Domain.IRepositories;

namespace Az.Administrativo.Application.Queries.TipoPacoteQueries.GetAllTiposPacote;

public class GetAllTiposPacoteQueryHandler : IRequestHandler<GetAllTiposPacoteQuery, OperationResult<List<GetAllTiposPacoteResponse>>>
{
    private readonly ITipoPacoteRepository _repository;

    public GetAllTiposPacoteQueryHandler(ITipoPacoteRepository repository)
    {
        _repository = repository;
    }

    public async Task<OperationResult<List<GetAllTiposPacoteResponse>>> Handle(GetAllTiposPacoteQuery request, CancellationToken cancellationToken)
    {
        var tiposPacote = await _repository.GetAllAsync();

        var response = tiposPacote
            .OrderBy(tp => tp.Ordem)
            .Select(tp => new GetAllTiposPacoteResponse(
                tp.Id<PERSON>ipoPacote,
                tp.Des<PERSON>o,
                tp.Ordem,
                tp.Ativo
            ))
            .ToList();

        return OperationResult.Result(response);
    }
}
