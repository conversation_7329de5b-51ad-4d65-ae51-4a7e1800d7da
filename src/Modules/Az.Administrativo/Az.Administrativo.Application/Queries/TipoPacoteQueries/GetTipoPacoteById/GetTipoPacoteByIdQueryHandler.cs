using MediatR;
using Az.Shared.Shared.Common;
using Az.Administrativo.Domain.IRepositories;

namespace Az.Administrativo.Application.Queries.TipoPacoteQueries.GetTipoPacoteById;

public class GetTipoPacoteByIdQueryHandler : IRequestHandler<GetTipoPacoteByIdQuery, OperationResult<GetTipoPacoteByIdResponse>>
{
    private readonly ITipoPacoteRepository _repository;

    public GetTipoPacoteByIdQueryHandler(ITipoPacoteRepository repository)
    {
        _repository = repository;
    }

    public async Task<OperationResult<GetTipoPacoteByIdResponse>> Handle(GetTipoPacoteByIdQuery request, CancellationToken cancellationToken)
    {
        var tipoPacote = await _repository.GetByIdAsync(request.IdTipoPacote);

        if (tipoPacote == null)
            return OperationResult.Error<GetTipoPacoteByIdResponse>(["Tipo de pacote não encontrado"]);

        var response = new GetTipoPacoteByIdResponse(
            tipoPacote.IdTipoPacote,
            tipoPacote.Descricao,
            tipoPacote.Ordem,
            tipoPacote.Ativo
        );

        return OperationResult.Result(response);
    }
}
