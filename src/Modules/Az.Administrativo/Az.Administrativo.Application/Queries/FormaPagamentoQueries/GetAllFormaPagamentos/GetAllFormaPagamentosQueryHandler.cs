using MediatR;
using Az.Administrativo.Domain.IRepositories;
using Az.Shared.Shared.Common;
using Az.Administrativo.Application.Queries.FormaPagamentoQueries.GetAllFormaPagamentos;

namespace Az.Administrativo.Application.Queries.FormaPagamentoQueries.GetAllFormaPagamentos;

public class GetAllFormaPagamentosQueryHandler : IRequestHandler<GetAllFormaPagamentosQuery, OperationResult<List<GetAllFormaPagamentosResponse>>>
{
    private readonly IFormaPagamentoRepository _repository;
    public GetAllFormaPagamentosQueryHandler(IFormaPagamentoRepository repository)
    {
        _repository = repository;
    }

    public async Task<OperationResult<List<GetAllFormaPagamentosResponse>>> <PERSON>le(GetAllFormaPagamentosQuery request, CancellationToken cancellationToken)
    {
        var formas = await _repository.GetAllAsync();
        var result = formas.Select(f => new GetAllFormaPagamentosResponse(
            f.IdFormaPagamento,
            f.<PERSON>,
            f.<PERSON>,
            f.<PERSON>,
            f.<PERSON>,
            f.<PERSON>cimo,
            f.Ativo
        )).ToList();
        return OperationResult.Result(result);
    }
}