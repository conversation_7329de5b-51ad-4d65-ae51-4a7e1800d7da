# Mó<PERSON>lo Administrativo

Este módulo é responsável pelo gerenciamento de regras administrativas do restaurante, como horários de funcionamento e feriados.

---

## Estrutura do Projeto

| Projeto | Descrição |
|---------|-----------|
| **Az.Administrativo.Domain** | Entidades de domínio e regras de negócio |
| **Az.Administrativo.Application** | Comandos, queries, DTOs e validações |
| **Az.Administrativo.Infrastructure** | Implementação de persistência e integrações |
| **Az.Administrativo.IntegrationEvents** | Eventos de integração para comunicação entre módulos |
| **Az.Administrativo.Api** | Endpoints e configurações específicas do módulo |
| **Az.Administrativo.Tests** | Testes unitários e de integração |

---

## Estrutura da API

```
MapEndpoints
  ├── MapFeriadoEndpoints
  ├── MapFormaPagamentos
  ├── MapHorarioFuncionamentoEndpoints
  └── MapTermoCondicaoEndpoints
```

### Configuração

- Cada map criado deve ser declarado no arquivo `AdministrativoConfigApp`
- No endpoint deve ser adicionado o `RequireAuthorization`
- Configurar status codes de acordo com o retorno

---

## Estrutura da Application

```
src/Modules/Az.Administrativo/Az.Administrativo.Application/
├── Commands/
│   ├── FeriadoCommands/
│   │   ├── CreateFeriado/
│   │   │   ├── CreateFeriadoCommand.cs
│   │   │   ├── CreateFeriadoCommandHandler.cs
│   │   │   └── CreateFeriadoCommandValidator.cs
│   │   ├── UpdateFeriado/
│   │   └── DeleteFeriado/
│   ├── HorarioFuncionamentoCommands/
│   └── FormaPagamentoCommands/
└── Queries/
    ├── FeriadoQueries/
    │   ├── GetFeriadoById/
    │   │   ├── GetAllFeriadosQuery.cs
    │   │   ├── GetAllFeriadosQueryHandler.cs
    │   │   ├── GetAllFeriadosQueryValidator.cs
    │   │   └── GetAllFeriadosResponse.cs
    │   └── GetAllFeriados/
    ├── HorarioFuncionamentoQueries/
    └── FormaPagamentoQueries/
```

### Regras de Organização

- Todos os comandos de uma entidade ficam em `Commands/NomeEntidadeCommands/`
- Todos os queries de uma entidade ficam em `Queries/NomeEntidadeQueries/`
- Todas as queries devem ter seu próprio response
- Cada operação (Create, Update, etc.) tem sua própria subpasta
- **Todas as queries com parâmetros devem ter um validator** - mesmo que seja apenas para validar um ID

> **Importante:** Sempre que criar um novo comando ou query, siga este padrão para manter o projeto organizado e facilitar a localização dos arquivos.

---

## Estrutura da Domain

```
Domain/
├── Entities/
│   ├── Feriado.cs
│   ├── FormaPagamentos.cs
│   └── HorarioFuncionamentoEndpoints.cs
├── Enums/
│   └── TipoFormaPagamentoEnum.cs
├── Structs/
│   └── Peso.cs
└── IRepositories/
    └── IFeriadoRepository.cs
```

### Regras de Implementação

- Todos os repositórios devem estender de `IRepository`
- Todas as entidades devem estender de `BaseEntidade`
- Usar `private set` para propriedades de entidades
- Toda entidade deve ter métodos de alteração (alguns já existem na `BaseEntidade`)

---

## Estrutura da Infrastructure

```
Infrastructure/
├── Mappings/
│   ├── FeriadoMap.cs
│   ├── FormaPagamentosMap.cs
│   └── HorarioFuncionamentoEndpointsMap.cs
└── Repositories/
    └── IFeriadoRepository.cs
```
- Todas as entidades devem ter seu respectivo map
- Todos os repositórios devem estender de `Repository<'NomeEntidade', AdministrativoDbContext>`, `'INomeEntidadeRepository'`
- Os métodos de repositório devem ser assíncronos
- Os métodos de repositório devem ser públicos

## Estrutura da Testes

```
Tests/
└── Unit/
    ├── Commands/   
    │   │── FeriadoCommands/
    │   │   ├── CreateFeriado/
    │   │   │   ├── CreateFeriadoCommandHandlerTests.cs
    │   │   │   └── CreateFeriadoCommandValidator.cs/
    │   │   ├── DeleteFeriado/
    │   │   └── UpdateFeriado/
    └── Queries/
        └── FeriadoQueries/
            ├── GetAllFeriados/
            │   └── GetAllFeriadosQueryHandlerTests.cs
            └── GetFeriadoById/
                │── GetFeriadoByIdQueryHandlerTests.cs
                └── GetFeriadoByIdQueryValidatorTests.cs    
```

- Caso algum endpoint possuir parametros devem

### Regras de Implementação

- Não há necessidade de declarar DbSet em `AdministrativoDbContext`
- Todo Map deve estender de `BaseEntidadeMap`
- Todo repositório deve estender de `Repository<'NomeEntidade', AdministrativoDbContext>`, `'INomeEntidadeRepository'`

### Interface IRepository

Todos os repositórios devem implementar os métodos da interface base:

```csharp
public interface IRepository<TEntity> where TEntity : class
{
    Task<TEntity?> GetByIdAsync(Guid id);
    Task<IEnumerable<TEntity>> GetAllAsync();
    Task<IEnumerable<TEntity>> GetAllActiveAsync();
    Task<IEnumerable<TEntity>> GetPagedAsync(int page, int pageSize);
    Task AddAsync(TEntity entity);
    Task UpdateAsync(TEntity entity);
    Task DeleteAsync(TEntity entity, Guid idUsuario);
}
```

**Observações sobre os métodos:**
- `GetAllAsync()`: Retorna todas as entidades não excluídas (filtro de soft delete aplicado)
- `GetAllActiveAsync()`: Retorna apenas entidades ativas (filtro adicional por campo `Ativo`)
- `GetPagedAsync()`: Implementa paginação para listagens grandes
- `DeleteAsync()`: Recebe o `idUsuario` para auditoria do soft delete

---

## Estrutura da IntegrationEvents

```
IntegrationEvents/
└── Events/
    └── FeriadoChangedIntegrationEvent.cs
```

---

## Persistência: Repositórios e Unit of Work

Utilizamos o padrão **Repository** para abstração do acesso a dados e o padrão **Unit of Work** para garantir atomicidade e controle transacional das operações de escrita.

> **Recomendação:** Todos os repositórios devem ser utilizados por meio de uma Unit of Work, facilitando o commit/rollback de múltiplas operações em um mesmo contexto.

### Declaração do Unit of Work na Application

```csharp
private readonly IUnitOfWork<AdministrativoDbContext> _unitOfWork;
```

### ICurrentUser para Auditoria

Para operações de **Update** e **Delete**, utilizar o `ICurrentUser` para informar o usuário responsável pela alteração/exclusão:

```csharp
private readonly ICurrentUser _currentUser;
```

---

## Decisões de Design

| Decisão | Descrição |
|---------|-----------|
| **Sem Exceptions** | Não utilizamos exceptions para regras de negócio |
| **Sem Try-Catch** | Não utilizamos try-catch nos handlers - deixamos as exceções propagarem |
| **Encapsulamento** | Propriedades com `private set` |
| **Métodos Controlados** | Alterações via métodos públicos |
| **Feriados Simples** | Restaurante fechado em feriados (sem horários especiais) |
| **Múltiplos Horários** | Permitido vários horários para o mesmo dia |
| **Unit of Work** | Controle transacional obrigatório |

### Regra: Não Usar Try-Catch

**❌ NÃO fazer:**
```csharp
public async Task<OperationResult<T>> Handle(Query request, CancellationToken cancellationToken)
{
    try
    {
        // lógica do handler
        return OperationResult.Result(data);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Erro no handler");
        throw; // ou return OperationResult.Error()
    }
}
```

**✅ Fazer:**
```csharp
public async Task<OperationResult<T>> Handle(Query request, CancellationToken cancellationToken)
{
    // lógica do handler - deixar exceções propagarem naturalmente
    return OperationResult.Result(data);
}
```

**Exceções permitidas:**
- **Graceful degradation para cache** - Try-catch apenas para funcionalidades não críticas como cache Redis
- **Validações de entrada** - Apenas quando necessário para transformar exceções técnicas em erros de negócio

**Exemplo de graceful degradation permitido:**
```csharp
private async Task<T?> TentarObterDoCache(string cacheKey)
{
    try
    {
        return await _cacheService.GetAsync<T>(cacheKey);
    }
    catch (Exception ex)
    {
        _logger.LogWarning(ex, "Erro ao acessar cache. Continuando sem cache");
        return null; // Graceful degradation
    }
}
```

---

## Commands (Operações de Escrita)

### Feriado

#### CreateFeriadoCommand
- **Finalidade:** Cria um novo feriado
- **Localização:** `Commands/Feriado/CreateFeriado/`
- **Retorno:** `OperationResult<Guid>`
- **Endpoint:** `POST /v1/feriados`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`

**Validações:**
- `Data` é obrigatória e não pode ser default
- `Descricao` é obrigatória e não pode ser vazia

**Exemplo de payload:**
```json
{
  "data": "2024-12-25",
  "descricao": "Natal"
}
```

#### UpdateFeriadoCommand
- **Finalidade:** Atualiza um feriado existente
- **Localização:** `Commands/Feriado/UpdateFeriado/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `PUT /v1/feriados/{id}`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`, `ICurrentUser`

**Validações:**
- `Id` é obrigatório
- `Data` é obrigatória e não pode ser default
- `Descricao` é obrigatória e não pode ser vazia

**Exemplo de payload:**
```json
{
  "id": "<guid>",
  "data": "2024-12-25",
  "descricao": "Natal"
}
```

#### DeleteFeriadoCommand
- **Finalidade:** Marca um feriado como excluído (soft delete)
- **Localização:** `Commands/Feriado/DeleteFeriado/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `DELETE /v1/feriados/{id}`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`, `ICurrentUser`
- **Validações:** `Id` é obrigatório

---

### HorarioFuncionamento

#### CreateHorarioFuncionamentoCommand
- **Finalidade:** Cria um novo horário de funcionamento
- **Localização:** `Commands/HorarioFuncionamentoCommands/CreateHorarioFuncionamento/`
- **Retorno:** `OperationResult<Guid>`
- **Endpoint:** `POST /v1/horarios-funcionamento/create`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`

**Validações:**
- `HoraAbertura` e `HoraFechamento` são obrigatórios
- `HoraAbertura` deve ser menor que `HoraFechamento`
- `HoraAbertura` e `HoraFechamento` não podem ser iguais

**Exemplo de payload:**
```json
{
  "diaSemana": "Monday",
  "horaAbertura": "08:00",
  "horaFechamento": "18:00"
}
```

#### UpdateHorarioFuncionamentoCommand
- **Finalidade:** Atualiza um horário de funcionamento existente
- **Localização:** `Commands/HorarioFuncionamentoCommands/UpdateHorarioFuncionamento/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `PUT /v1/horarios-funcionamento/{id}`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`, `ICurrentUser`
- **Validações:** Mesmas do Create + `Id` obrigatório

#### DeleteHorarioFuncionamentoCommand
- **Finalidade:** Remove (soft delete) um horário de funcionamento
- **Localização:** `Commands/HorarioFuncionamentoCommands/DeleteHorarioFuncionamento/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `DELETE /v1/horarios-funcionamento/{id}`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`, `ICurrentUser`
- **Validações:** `Id` é obrigatório

---

### FormaPagamento

#### CreateFormaPagamentoCommand
- **Finalidade:** Cria uma nova forma de pagamento
- **Localização:** `Commands/FormaPagamentoCommands/CreateFormaPagamento/`
- **Retorno:** `OperationResult<Guid>`
- **Endpoint:** `POST /v1/formas-pagamento`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`

**Validações:**
- `Nome`: obrigatório, até 100 caracteres
- `Tipo`: obrigatório, valor válido do enum
- `MaximoParcelas`: obrigatório se tipo for parcelado (>1), nulo para outros tipos
- `Observacao`: até 255 caracteres, opcional
- `Acrescimo`: decimal >= 0, opcional

**Exemplo de payload:**
```json
{
  "nome": "Cartão de Crédito",
  "tipo": 3,
  "maximoParcelas": 12,
  "observacao": "Aceita Visa/Master",
  "acrescimo": 2.5
}
```

#### UpdateFormaPagamentoCommand
- **Finalidade:** Atualiza uma forma de pagamento existente
- **Localização:** `Commands/FormaPagamentoCommands/UpdateFormaPagamento/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `PUT /v1/formas-pagamento/{id}`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`, `ICurrentUser`

**Validações:**
- `IdFormaPagamento`: obrigatório
- Demais campos: mesmas regras do Create

#### DeleteFormaPagamentoCommand
- **Finalidade:** Marca uma forma de pagamento como excluída (soft delete)
- **Localização:** `Commands/FormaPagamentoCommands/DeleteFormaPagamento/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `DELETE /v1/formas-pagamento/{id}`
- **Dependências:** `IUnitOfWork<AdministrativoDbContext>`, `ICurrentUser`
- **Validações:** `IdFormaPagamento`: obrigatório

---

## Queries (Operações de Leitura)

### Feriado

#### GetAllFeriadosQuery
- **Finalidade:** Retorna todos os feriados cadastrados (exceto excluídos)
- **Localização:** `Queries/FeriadoQueries/GetAllFeriados/`
- **Retorno:** `OperationResult<List<GetAllFeriadosResponse>>`
- **Endpoint:** `GET /v1/feriados`
- **Regras:**
  - Retorna apenas feriados não excluídos (soft delete)
  - Não há filtros adicionais

#### GetFeriadoByIdQuery
- **Finalidade:** Retorna um feriado pelo Id
- **Localização:** `Queries/FeriadoQueries/GetFeriadoById/`
- **Retorno:** `OperationResult<GetFeriadoByIdResponse>`
- **Endpoint:** `GET /v1/feriados/{id}`
- **Regras:**
  - Retorna erro se o feriado não for encontrado
  - Retorna apenas feriados não excluídos

---

### HorarioFuncionamento

#### GetAllHorariosFuncionamentoQuery
- **Finalidade:** Retorna todos os horários de funcionamento cadastrados (exceto excluídos)
- **Localização:** `Queries/HorarioFuncionamentoQueries/GetAllHorariosFuncionamento/`
- **Retorno:** `OperationResult<List<GetAllHorariosFuncionamentoResponse>>`
- **Endpoint:** `GET /v1/horarios-funcionamento`
- **Regras:** Retorna todos os horários não excluídos

#### GetHorarioFuncionamentoByIdQuery
- **Finalidade:** Retorna um horário de funcionamento pelo Id
- **Localização:** `Queries/HorarioFuncionamentoQueries/GetHorarioFuncionamentoById/`
- **Retorno:** `OperationResult<GetHorarioFuncionamentoByIdResponse>`
- **Endpoint:** `GET /v1/horarios-funcionamento/{id}`
- **Regras:** Erro se não encontrado ou excluído, `Id` obrigatório

#### GetDatasDisponiveisParaInicioQuery
- **Finalidade:** Retorna as próximas 20 datas disponíveis para início de contrato
- **Localização:** `Queries/HorarioFuncionamentoQueries/GetDatasDisponiveisParaInicio/`
- **Retorno:** `OperationResult<List<DateOnly>>`
- **Endpoint:** `GET /v1/horarios-funcionamento/datas-disponiveis-para-inicio`

**Regras de Negócio:**

1. **Lista dos próximos 20 dias:** O sistema retorna uma lista dos próximos 20 dias a partir da data atual.

2. **Verificação de horários de funcionamento:** O sistema verifica na tabela `HorarioFuncionamento` os horários de início e fim do expediente e os dias da semana cadastrados.

3. **Exclusão da data atual fora do expediente:** Caso o horário atual esteja fora do intervalo de funcionamento cadastrado para o dia atual, o sistema ignora a data atual e inicia a contagem a partir do próximo dia.

4. **Verificação de feriados:** O sistema verifica se existem feriados cadastrados ao longo do período e ignora essas datas na lista final.

**Comportamento detalhado:**
- Se hoje é segunda-feira e há expediente das 8h às 18h:
  - Se for 10h da manhã (dentro do expediente): hoje pode ser incluído
  - Se for 19h da noite (fora do expediente): hoje é ignorado, inicia de amanhã
- Se hoje não tem expediente cadastrado: hoje é ignorado, inicia de amanhã
- Feriados cadastrados são sempre ignorados, independente do dia da semana
- Se não houver dias abertos cadastrados: retorna os próximos 20 dias corridos

**Cache:**
- Utiliza cache Redis com TTL de 1 dia
- Cache é invalidado automaticamente quando horários de funcionamento ou feriados são alterados

**Exemplo de resposta:**
```json
[
  "2024-05-10",
  "2024-05-13",
  "2024-05-14",
  "..."
]
```

---

### FormaPagamento

#### GetAllFormaPagamentosQuery
- **Finalidade:** Retorna todas as formas de pagamento cadastradas
- **Localização:** `Queries/FormaPagamentoQueries/GetAllFormaPagamentos/`
- **Retorno:** `OperationResult<List<GetAllFormaPagamentosResponse>>`
- **Endpoint:** `GET /v1/formas-pagamento`
- **Regras:** Retorna todas as formas de pagamento, inclusive inativas/excluídas

#### GetFormaPagamentoByIdQuery
- **Finalidade:** Retorna uma forma de pagamento pelo Id
- **Localização:** `Queries/FormaPagamentoQueries/GetFormaPagamentoById/`
- **Retorno:** `OperationResult<GetFormaPagamentoByIdResponse>`
- **Endpoint:** `GET /v1/formas-pagamento/{id}`
- **Validações:** `IdFormaPagamento`: obrigatório
- **Regras:** Retorna erro se não encontrada

---

## Observações Importantes

| Item | Descrição |
|------|-----------|
| **Soft Delete** | Exclusão apenas marca como excluído, não remove do banco |
| **Campo Ativo** | Herdado de `BaseEntidade` |
| **Enum de Tipos** | Pix, Cartão Crédito à vista, Cartão Crédito parcelado, Débito, Dinheiro, Vale Refeição, Outro |

---

## Termos e Condições

### Commands

#### CreateTermoCondicaoCommand
- **Finalidade:** Cria um novo termo e condição
- **Localização:** `Commands/TermoCondicaoCommands/CreateTermoCondicao/`
- **Retorno:** `OperationResult<Guid>`
- **Endpoint:** `POST /api/v1/termos-condicoes`
- **Dependências:** `ITermoCondicaoRepository`, `IUnitOfWork`

**Validações:**
- `Titulo` é obrigatório, tamanho máximo 100 caracteres
- `Descricao` é obrigatória, tamanho máximo 2000 caracteres

#### UpdateTermoCondicaoCommand
- **Finalidade:** Atualiza um termo e condição existente
- **Localização:** `Commands/TermoCondicaoCommands/UpdateTermoCondicao/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `PUT /api/v1/termos-condicoes/{id}`
- **Dependências:** `ITermoCondicaoRepository`, `IUnitOfWork`

**Validações:**
- `IdTermoCondicao` é obrigatório
- `Titulo` é obrigatório, tamanho máximo 100 caracteres
- `Descricao` é obrigatória, tamanho máximo 2000 caracteres

#### DeleteTermoCondicaoCommand
- **Finalidade:** Marca um termo e condição como excluído (soft delete)
- **Localização:** `Commands/TermoCondicaoCommands/DeleteTermoCondicao/`
- **Retorno:** `OperationResult<bool>`
- **Endpoint:** `DELETE /api/v1/termos-condicoes/{id}`
- **Dependências:** `ITermoCondicaoRepository`, `IUnitOfWork`
- **Validações:** `IdTermoCondicao` é obrigatório

### Queries

#### GetAllTermosCondicoesQuery
- **Finalidade:** Retorna todos os termos e condições cadastrados (exceto excluídos)
- **Localização:** `Queries/TermoCondicaoQueries/GetAllTermosCondicoes/`
- **Retorno:** `OperationResult<List<GetAllTermosCondicoesResponse>>`
- **Endpoint:** `GET /api/v1/termos-condicoes`
- **Regras:** Retorna apenas termos não excluídos (soft delete)

#### GetTermoCondicaoByIdQuery
- **Finalidade:** Retorna um termo e condição pelo Id
- **Localização:** `Queries/TermoCondicaoQueries/GetTermoCondicaoById/`
- **Retorno:** `OperationResult<GetTermoCondicaoByIdResponse>`
- **Endpoint:** `GET /api/v1/termos-condicoes/{id}`
- **Validações:** `IdTermoCondicao` é obrigatório
- **Regras:** Retorna erro se o termo não for encontrado

---

# FUNCIONALIDADES TÉCNICAS

## Sistema de Cache Redis

O módulo Administrativo utiliza cache Redis para otimizar consultas frequentes e melhorar a performance da aplicação.

### Configuração

O cache Redis é configurado automaticamente através do serviço compartilhado `ICacheService` localizado no módulo `Az.Shared.Shared`.

```csharp
// Configuração no módulo Administrativo
// InstanceName vazio para evitar duplicação de prefixo
services.AddRedisCache(configuration, "");
```

### Estrutura de Chaves

As chaves de cache seguem um padrão hierárquico:
```
azfood:administrativo:{entidade}:{operacao}:{parametros}
```

**Exemplos:**
- `azfood:administrativo:datas-disponiveis:para-inicio:2024-01-15`
- `azfood:administrativo:feriados:all`
- `azfood:administrativo:horarios-funcionamento:all`

### TTL (Time To Live)

- **Datas Disponíveis:** 1 dia (24 horas)
- **Feriados:** 1 dia (24 horas)
- **Horários de Funcionamento:** 1 dia (24 horas)

### Invalidação Automática

O cache é invalidado automaticamente nas seguintes operações:

#### Horários de Funcionamento
- **Create:** Invalida cache de horários + datas disponíveis
- **Update:** Invalida cache de horários + datas disponíveis
- **Delete:** Invalida cache de horários + datas disponíveis

#### Feriados
- **Create:** Invalida cache de feriados + datas disponíveis
- **Update:** Invalida cache de feriados + datas disponíveis
- **Delete:** Invalida cache de feriados + datas disponíveis

### Regra Obrigatória: Invalidação de DatasDisponiveis

**⚠️ IMPORTANTE:** Todos os Commands que afetam horários de funcionamento ou feriados **DEVEM** invalidar o cache de `CacheKeys.Administrativo.DatasDisponiveis`.

#### Commands Obrigatórios:

**Horários de Funcionamento:**
```csharp
// CreateHorarioFuncionamentoCommandHandler
await _cacheInvalidationService.InvalidateHorariosFuncionamentoAsync(cancellationToken);

// UpdateHorarioFuncionamentoCommandHandler
await _cacheInvalidationService.InvalidateHorariosFuncionamentoAsync(cancellationToken);

// DeleteHorarioFuncionamentoCommandHandler
await _cacheInvalidationService.InvalidateHorariosFuncionamentoAsync(cancellationToken);
```

**Feriados:**
```csharp
// CreateFeriadoCommandHandler
await _cacheInvalidationService.InvalidateFeriadosAsync(cancellationToken);

// UpdateFeriadoCommandHandler
await _cacheInvalidationService.InvalidateFeriadosAsync(cancellationToken);

// DeleteFeriadoCommandHandler
await _cacheInvalidationService.InvalidateFeriadosAsync(cancellationToken);
```

#### Por que é obrigatório?

O cache de `DatasDisponiveis` depende diretamente de:
1. **Horários de funcionamento** - Para determinar quais dias da semana estão abertos
2. **Feriados** - Para excluir datas específicas da lista

Qualquer alteração nesses dados torna o cache de datas disponíveis **inválido** e pode causar:
- ❌ Datas incorretas sendo retornadas
- ❌ Feriados não sendo respeitados
- ❌ Horários alterados não sendo considerados

#### Implementação Automática

Os métodos `InvalidateHorariosFuncionamentoAsync()` e `InvalidateFeriadosAsync()` **automaticamente** chamam `InvalidateDatasDisponiveisAsync()`, garantindo consistência.

### Configuração de Prefixos

**⚠️ IMPORTANTE:** Para evitar duplicação de prefixos nas chaves de cache, o `instanceName` do Redis deve ser configurado como string vazia.

#### Problema Anterior:
```csharp
// ❌ INCORRETO - Causava duplicação
services.AddRedisCache(configuration, "AzFood-Administrativo");

// Resultado: AzFood-Administrativoazfood:administrativo:datas-disponiveis:para-inicio:2025-06-07
```

#### Solução Atual:
```csharp
// ✅ CORRETO - Evita duplicação
services.AddRedisCache(configuration, "");

// Resultado: azfood:administrativo:datas-disponiveis:para-inicio:2025-06-07
```

#### Por que isso acontecia?

O StackExchange.Redis automaticamente adiciona o `instanceName` como prefixo a todas as chaves. Como nossas chaves já incluem o prefixo hierárquico (`azfood:administrativo`), usar um `instanceName` não vazio causava duplicação.

#### Estrutura Final das Chaves:

```
azfood:administrativo:datas-disponiveis:para-inicio:2025-06-07
azfood:administrativo:feriados:all
azfood:administrativo:horarios:all
```

### Serviço de Invalidação

O `ICacheInvalidationService` é responsável por gerenciar a invalidação:

```csharp
public interface ICacheInvalidationService
{
    Task InvalidateDatasDisponiveisAsync(CancellationToken cancellationToken = default);
    Task InvalidateFeriadosAsync(CancellationToken cancellationToken = default);
    Task InvalidateHorariosFuncionamentoAsync(CancellationToken cancellationToken = default);
    Task InvalidateAllAsync(CancellationToken cancellationToken = default);
}
```

### Graceful Degradation

- Se o Redis estiver indisponível, a aplicação continua funcionando normalmente
- Falhas de cache não interrompem o fluxo principal da aplicação
- Logs estruturados registram problemas de cache para monitoramento

---

## Diretrizes para Futuras Documentações

### Estrutura Obrigatória do README

Para manter a organização e evitar bagunça no README, **SEMPRE** siga esta estrutura:

```
# Módulo [Nome]
## Estrutura do Projeto
## Estrutura da API
## Estrutura da Application
## Estrutura da Domain
## Estrutura da Infrastructure
## Estrutura da Testes
## Estrutura da IntegrationEvents
## Persistência: Repositórios e Unit of Work
## Decisões de Design
## Commands (Operações de Escrita)
## Queries (Operações de Leitura)
## Observações Importantes
## [Outras entidades - ex: Termos e Condições]
---
# FUNCIONALIDADES TÉCNICAS
## [Funcionalidades técnicas como Cache, Logs, etc.]
## Diretrizes para Futuras Documentações
```

### Regras para Adições

1. **Endpoints e Commands/Queries:** Sempre na seção correspondente (Commands ou Queries)
2. **Funcionalidades Técnicas:** Sempre na seção "FUNCIONALIDADES TÉCNICAS" no final
3. **Configurações:** Na seção "FUNCIONALIDADES TÉCNICAS"
4. **Regras de Negócio:** Dentro da documentação do endpoint específico
5. **Exemplos de Código:** Apenas em funcionalidades técnicas ou validações

### ❌ O que NÃO fazer

- ❌ Inserir funcionalidades técnicas no meio da documentação de endpoints
- ❌ Misturar configurações com documentação de API
- ❌ Adicionar seções sem seguir a estrutura definida
- ❌ Documentar funcionalidades técnicas junto com regras de negócio

### ✅ O que fazer

- ✅ Seguir a estrutura definida rigorosamente
- ✅ Adicionar funcionalidades técnicas na seção dedicada
- ✅ Manter separação clara entre API e funcionalidades técnicas
- ✅ Usar a seção "Observações Importantes" para informações gerais