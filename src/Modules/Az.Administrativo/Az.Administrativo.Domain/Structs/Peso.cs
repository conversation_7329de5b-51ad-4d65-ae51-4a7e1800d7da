namespace Az.Administrativo.Domain.Structs;

/// <summary>
/// Struct que define os tipos de peso disponíveis no sistema.
/// Contém identificadores únicos para cada categoria de peso.
/// </summary>
public struct Peso
{
    /// <summary>
    /// Peso de 200 gramas
    /// </summary>
    public static Guid Gm200 = Guid.Parse("D36D2A85-8851-40C2-B2DB-8DBD0C7F5473");
    
    /// <summary>
    /// Peso de 300 gramas
    /// </summary>
    public static Guid Gm300 = Guid.Parse("00149AA6-6F11-4395-B73F-66AB53F34251");
    
    /// <summary>
    /// Peso de 400 gramas
    /// </summary>
    public static Guid Gm400 = Guid.Parse("7D75D436-2173-496A-9D2F-165519671084");
}
