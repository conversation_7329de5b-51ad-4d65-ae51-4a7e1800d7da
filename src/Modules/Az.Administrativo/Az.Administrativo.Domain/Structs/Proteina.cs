namespace Az.Administrativo.Domain.Structs;

/// <summary>
/// Struct que define os tipos de proteína disponíveis no sistema.
/// Contém identificadores únicos para cada tipo de proteína e suas combinações.
/// </summary>
public struct Proteina
{
    #region Proteínas Individuais
    
    /// <summary>
    /// Carne Vermelha
    /// </summary>
    public static Guid CarneVermelha = Guid.Parse("6B34353B-6D2F-4D88-893B-39B985B95B01");
    
    /// <summary>
    /// Frango
    /// </summary>
    public static Guid Frango = Guid.Parse("6B34353B-6D2F-4D88-893B-39B985B95B02");
    
    /// <summary>
    /// Peixe
    /// </summary>
    public static Guid Peixe = Guid.Parse("6B34353B-6D2F-4D88-893B-39B985B95B03");
    
    /// <summary>
    /// Carne Suína
    /// </summary>
    public static Guid Suina = Guid.Parse("6B34353B-6D2F-4D88-893B-39B985B95B04");
    
    #endregion

    #region Combinações de Duas Proteínas
    
    /// <summary>
    /// Combinação: Carne Vermelha + Frango
    /// </summary>
    public static Guid CarneVermelhaFrango = Guid.Parse("6F776605-9B55-4A5E-89C2-92A830B98D00");
    
    /// <summary>
    /// Combinação: Carne Vermelha + Peixe
    /// </summary>
    public static Guid CarneVermelhaPeixe = Guid.Parse("9772DEF7-03BA-4EEC-A866-9E2417F768F8");
    
    /// <summary>
    /// Combinação: Carne Vermelha + Suína
    /// </summary>
    public static Guid CarneVermelhaSuina = Guid.Parse("D92532F2-A82A-4177-A934-4D75162B6BCA");
    
    /// <summary>
    /// Combinação: Frango + Peixe
    /// </summary>
    public static Guid FrangoPeixe = Guid.Parse("D2B013F3-879A-4C18-B711-1EF053CE37FA");
    
    /// <summary>
    /// Combinação: Frango + Suína
    /// </summary>
    public static Guid FrangoSuina = Guid.Parse("B1D9A9AB-B2F4-467D-B694-D8482C375A60");
    
    /// <summary>
    /// Combinação: Peixe + Suína
    /// </summary>
    public static Guid PeixeSuina = Guid.Parse("7FD33A56-BE37-4DB5-98F4-5AD5A59F941D");
    
    #endregion

    #region Combinações de Três Proteínas
    
    /// <summary>
    /// Combinação: Carne Vermelha + Frango + Peixe
    /// </summary>
    public static Guid CarneVermelhaFrangoPeixe = Guid.Parse("C93D81B0-9D06-476B-8C63-C84C5EC81C35");
    
    /// <summary>
    /// Combinação: Carne Vermelha + Frango + Suína
    /// </summary>
    public static Guid CarneVermelhaFrangoSuina = Guid.Parse("EC95E58E-7752-452B-A96A-75D95B379712");
    
    /// <summary>
    /// Combinação: Carne Vermelha + Peixe + Suína
    /// </summary>
    public static Guid CarneVermelhaPeixeSuina = Guid.Parse("F10D40C1-0B44-4200-91C9-B3283BEF02A5");
    
    /// <summary>
    /// Combinação: Frango + Peixe + Suína
    /// </summary>
    public static Guid FrangoPeixeSuina = Guid.Parse("436F0A8B-6597-487E-AB7C-CE431E6C7A2C");
    
    #endregion

    #region Combinação de Todas as Proteínas
    
    /// <summary>
    /// Combinação: Carne Vermelha + Frango + Peixe + Suína
    /// </summary>
    public static Guid CarneVermelhaFrangoPeixeSuina = Guid.Parse("942A3DF1-AD7C-4120-83AF-014554574571");
    
    #endregion
}
