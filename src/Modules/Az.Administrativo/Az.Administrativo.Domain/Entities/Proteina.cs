using Az.Shared.Shared.Entities;

namespace Az.Administrativo.Domain.Entities;

public class Proteina : BaseEntidade
{
    public Guid IdProteina { get; private set; }
    public string Descricao { get; private set; }

    protected Proteina()
    {
        Descricao = string.Empty;
    }

    public Proteina(Guid idProteina, string descricao)
    {
        IdProteina = idProteina;
        Descricao = descricao;
    }
}
