using Az.Shared.Shared.Entities;

namespace Az.Administrativo.Domain.Entities;

public class Peso : BaseEntidade
{
    public Guid IdPeso { get; private set; }
    public string Nome { get; private set; }
    public string Descricao { get; private set; }
    public int Ordem { get; private set; }

    protected Peso()
    {
        Nome = string.Empty;
        Descricao = string.Empty;
    }

    public Peso(string nome, string descricao, int ordem)
    {
        IdPeso = Guid.NewGuid();
        Nome = nome;
        Descricao = descricao;
        Ordem = ordem;
    }

    public Peso(Guid idPeso, string nome, string descricao, int ordem)
    {
        IdPeso = idPeso;
        Nome = nome;
        Descricao = descricao;
        Ordem = ordem;
    }

    public void AlterarNome(string novoNome)
        => Nome = novoNome;

    public void AlterarDescricao(string novaDescricao)
        => Descricao = novaDescricao;

    public void AlterarOrdem(int novaOrdem)
        => Ordem = novaOrdem;


}
