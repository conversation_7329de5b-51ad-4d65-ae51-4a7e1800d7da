using Az.Shared.Shared.Entities;

namespace Az.Administrativo.Domain.Entities;

public class TipoPacote : BaseEntidade
{
    public Guid IdTipoPacote { get; private set; }
    public string Descricao { get; private set; }
    public int Ordem { get; private set; }

    protected TipoPacote()
    {
        Descricao = string.Empty;
    }

    public TipoPacote(string descricao, int ordem)
    {
        IdTipoPacote = Guid.NewGuid();
        Descricao = descricao;
        Ordem = ordem;
    }

    public void AlterarDescricao(string novaDescricao)
        => Descricao = novaDescricao;

    public void AlterarOrdem(int novaOrdem)
        => Ordem = novaOrdem;
}
