using Xunit;
using Moq;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Az.Administrativo.Application.Queries.FormaPagamentoQueries.GetAllFormaPagamentos;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.Enums;

public class GetAllFormaPagamentosQueryHandlerTests
{
    [Fact]
    public async Task Deve_Retornar_Todas_As_Formas_De_Pagamento()
    {
        var repoMock = new Mock<IFormaPagamentoRepository>();
        repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<FormaPagamento>
        {
            new FormaPagamento("Pix", TipoFormaPagamentoEnum.Pix),
            new FormaPagamento("Cartão", TipoFormaPagamentoEnum.CartaoCreditoAVista)
        });
        var handler = new GetAllFormaPagamentosQueryHandler(repoMock.Object);
        var result = await handler.Handle(new GetAllFormaPagamentosQuery(), CancellationToken.None);
        Assert.True(result.Success);
        Assert.Equal(2, result.Data!.Count);
    }
}