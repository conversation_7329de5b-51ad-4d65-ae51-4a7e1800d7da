using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Queries.TermoCondicaoQueries.GetTermoCondicaoById;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;

namespace Az.Administrativo.Tests.Unit.Queries.TermoCondicaoQueries.GetTermoCondicaoById;

public class GetTermoCondicaoByIdQueryHandlerTests
{
    private readonly Mock<ITermoCondicaoRepository> _repoMock;
    private readonly GetTermoCondicaoByIdQueryHandler _handler;

    public GetTermoCondicaoByIdQueryHandlerTests()
    {
        _repoMock = new Mock<ITermoCondicaoRepository>();
        _handler = new GetTermoCondicaoByIdQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar termo e condição por id com sucesso")]
    public async Task Deve_Retornar_TermoCondicao_Por_Id_Com_Sucesso()
    {
        // Arrange
        var termoCondicao = new TermoCondicao("Termos de Uso", "Descrição dos termos de uso");
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(termoCondicao);
        var query = new GetTermoCondicaoByIdQuery(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal("Termos de Uso", result.Data.Titulo);
        Assert.Equal("Descrição dos termos de uso", result.Data.Descricao);
    }

    [Fact(DisplayName = "Deve retornar erro se termo e condição não encontrado")]
    public async Task Deve_Retornar_Erro_Se_Nao_Encontrado()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync((TermoCondicao)null!);
        var query = new GetTermoCondicaoByIdQuery(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Termo e Condição não encontrado.", result.Messages!);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro")]
    public async Task Deve_Lancar_Excecao_Se_Erro()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ThrowsAsync(new Exception("Erro de teste"));
        var query = new GetTermoCondicaoByIdQuery(Guid.NewGuid());

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
    }
}
