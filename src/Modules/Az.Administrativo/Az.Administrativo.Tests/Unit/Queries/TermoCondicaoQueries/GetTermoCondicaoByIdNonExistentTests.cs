using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Queries.TermoCondicaoQueries.GetTermoCondicaoById;
using Az.Administrativo.Domain.IRepositories;

namespace Az.Administrativo.Tests.Unit.Queries.TermoCondicaoQueries;

public class GetTermoCondicaoByIdNonExistentTests
{
    private readonly Mock<ITermoCondicaoRepository> _repoMock;
    private readonly GetTermoCondicaoByIdQueryHandler _handler;

    public GetTermoCondicaoByIdNonExistentTests()
    {
        _repoMock = new Mock<ITermoCondicaoRepository>();
        _handler = new GetTermoCondicaoByIdQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar mensagem específica quando termo e condição não existir")]
    public async Task Deve_Retornar_Mensagem_Especifica_Quando_Nao_Existir()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync((Domain.Entities.TermoCondicao)null!);
        var query = new GetTermoCondicaoByIdQuery(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Termo e Condição não encontrado.", result.Messages![0]);
        Assert.Null(result.Data);
    }

    [Fact(DisplayName = "Deve retornar erro correto quando ID informado for Guid.Empty")]
    public async Task Deve_Retornar_Erro_Quando_ID_Vazio()
    {
        // Arrange - Repostas diferentes para diferentes IDs
        _repoMock.Setup(r => r.GetByIdAsync(Guid.Empty)).ReturnsAsync((Domain.Entities.TermoCondicao)null!);
        var query = new GetTermoCondicaoByIdQuery(Guid.Empty);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("não encontrado", result.Messages?[0]);
    }

    [Fact(DisplayName = "Deve validar comportamento quando repository retorna nulo")]
    public async Task Deve_Validar_Comportamento_Quando_Repository_Retorna_Nulo()
    {
        // Arrange - Explicitamente configurando retorno nulo
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>()))
                .ReturnsAsync(() => null);
        var query = new GetTermoCondicaoByIdQuery(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        _repoMock.Verify(r => r.GetByIdAsync(It.IsAny<Guid>()), Times.Once);
    }
}
