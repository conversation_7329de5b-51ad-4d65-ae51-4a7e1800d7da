using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Queries.ProteinaQueries.GetAllProteinas;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;

namespace Az.Administrativo.Tests.Unit.Queries.ProteinaQueries.GetAllProteinas;

public class GetAllProteinasQueryHandlerTests
{
    private readonly Mock<IProteinaRepository> _repoMock;
    private readonly GetAllProteinasQueryHandler _handler;

    public GetAllProteinasQueryHandlerTests()
    {
        _repoMock = new Mock<IProteinaRepository>();
        _handler = new GetAllProteinasQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar todas as proteínas com sucesso")]
    public async Task Deve_Retornar_Todas_Proteinas_Com_Sucesso()
    {
        // Arrange
        var proteinas = new List<Proteina>
        {
            new Proteina(Guid.Parse("D36D2A85-8851-40C2-B2DB-8DBD0C7F5473"), "Carne Vermelha"),
            new Proteina(Guid.Parse("00149AA6-6F11-4395-B73F-66AB53F34251"), "Frango"),
            new Proteina(Guid.Parse("7D75D436-2173-496A-9D2F-165519671084"), "Peixe")
        };
        _repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(proteinas);
        var query = new GetAllProteinasQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(3, result.Data.Count);
        Assert.Equal("Carne Vermelha", result.Data[0].Descricao);
        Assert.Equal("Frango", result.Data[1].Descricao);
        Assert.Equal("Peixe", result.Data[2].Descricao);
    }

    [Fact(DisplayName = "Deve retornar lista vazia quando não há proteínas")]
    public async Task Deve_Retornar_Lista_Vazia_Quando_Nao_Ha_Proteinas()
    {
        // Arrange
        _repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Proteina>());
        var query = new GetAllProteinasQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Empty(result.Data);
    }

    [Fact(DisplayName = "Deve chamar o repositório uma vez")]
    public async Task Deve_Chamar_Repositorio_Uma_Vez()
    {
        // Arrange
        _repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Proteina>());
        var query = new GetAllProteinasQuery();

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _repoMock.Verify(r => r.GetAllAsync(), Times.Once);
    }
}
