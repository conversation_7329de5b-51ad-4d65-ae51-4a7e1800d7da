using System;
using Xunit;
using Az.Administrativo.Application.Queries.ProteinaQueries.GetProteinaById;

namespace Az.Administrativo.Tests.Unit.Queries.ProteinaQueries.GetProteinaById;

public class GetProteinaByIdQueryValidatorTests
{
    private readonly GetProteinaByIdQueryValidator _validator;

    public GetProteinaByIdQueryValidatorTests()
    {
        _validator = new GetProteinaByIdQueryValidator();
    }

    [Fact(DisplayName = "Deve ser válido quando id é fornecido")]
    public void Deve_Ser_Valido_Quando_Id_E_Fornecido()
    {
        // Arrange
        var query = new GetProteinaByIdQuery(Guid.NewGuid());

        // Act
        var result = _validator.Validate(query);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    [Fact(DisplayName = "Deve ser inválido quando id é vazio")]
    public void Deve_Ser_Invalido_Quando_Id_E_Vazio()
    {
        // Arrange
        var query = new GetProteinaByIdQuery(Guid.Empty);

        // Act
        var result = _validator.Validate(query);

        // Assert
        Assert.False(result.IsValid);
        Assert.Single(result.Errors);
        Assert.Equal("Id da proteína é obrigatório", result.Errors[0].ErrorMessage);
    }

    [Theory(DisplayName = "Deve validar diferentes cenários de id")]
    [InlineData("00000000-0000-0000-0000-000000000000", false)] // Guid.Empty
    [InlineData("D36D2A85-8851-40C2-B2DB-8DBD0C7F5473", true)]  // Guid válido
    [InlineData("12345678-1234-1234-1234-123456789012", true)]  // Outro Guid válido
    public void Deve_Validar_Diferentes_Cenarios_De_Id(string guidString, bool esperado)
    {
        // Arrange
        var guid = Guid.Parse(guidString);
        var query = new GetProteinaByIdQuery(guid);

        // Act
        var result = _validator.Validate(query);

        // Assert
        Assert.Equal(esperado, result.IsValid);
    }
}
