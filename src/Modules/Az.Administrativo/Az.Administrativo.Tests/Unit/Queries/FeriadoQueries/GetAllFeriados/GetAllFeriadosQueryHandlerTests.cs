using Az.Administrativo.Application.Queries.FeriadoQueries.GetAllFeriados;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.IRepositories;
using Moq;

namespace Az.Administrativo.Tests.Unit.Queries.FeriadoQueries.GetAllFeriados;

public class GetAllFeriadosQueryHandlerTests
{
    private readonly Mock<IFeriadoRepository> _repoMock = new();
    private readonly GetAllFeriadosQueryHandler _handler;

    public GetAllFeriadosQueryHandlerTests()
    {
        _handler = new GetAllFeriadosQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar lista de feriados")]
    public async Task Deve_Retornar_Lista_De_Feriados()
    {
        // Arrange
        var feriados = new List<Feriado>
        {
            new Feriado(DateOnly.FromDateTime(DateTime.Today), "Feriado 1"),
            new Feriado(DateOnly.FromDateTime(DateTime.Today.AddDays(1)), "Feriado 2")
        };
        _repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(feriados);
        var query = new GetAllFeriadosQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.Data!.Count);
        Assert.Equal("Feriado 1", result.Data[0].Descricao);
        Assert.Equal("Feriado 2", result.Data[1].Descricao);
    }

    [Fact(DisplayName = "Deve retornar lista vazia se não houver feriados")]
    public async Task Deve_Retornar_Lista_Vazia()
    {
        // Arrange
        _repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Feriado>());
        var query = new GetAllFeriadosQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Empty(result.Data!);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro inesperado")]
    public async Task Deve_Lancar_Excecao_Se_Erro()
    {
        // Arrange
        _repoMock.Setup(r => r.GetAllAsync()).ThrowsAsync(new Exception("Erro de infra"));
        var query = new GetAllFeriadosQuery();

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
    }
}