using Xunit;
using Moq;
using Az.Administrativo.Application.Queries.TipoPacoteQueries.GetTipoPacoteById;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;

namespace Az.Administrativo.Tests.Unit.Queries.TipoPacoteQueries.GetTipoPacoteById;

public class GetTipoPacoteByIdQueryHandlerTests
{
    private readonly Mock<ITipoPacoteRepository> _repoMock;
    private readonly GetTipoPacoteByIdQueryHandler _handler;

    public GetTipoPacoteByIdQueryHandlerTests()
    {
        _repoMock = new Mock<ITipoPacoteRepository>();
        _handler = new GetTipoPacoteByIdQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar tipo de pacote por ID")]
    public async Task Deve_Retornar_TipoPacote_Por_Id()
    {
        // Arrange
        var id = Guid.NewGuid();
        var tipoPacote = new TipoPacote("Pacote Premium", 1);
        typeof(TipoPacote).GetProperty("IdTipoPacote")!.SetValue(tipoPacote, id);

        _repoMock.Setup(r => r.GetByIdAsync(id)).ReturnsAsync(tipoPacote);

        var query = new GetTipoPacoteByIdQuery(id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(id, result.Data.IdTipoPacote);
        Assert.Equal("Pacote Premium", result.Data.Descricao);
        Assert.Equal(1, result.Data.Ordem);
        Assert.True(result.Data.Ativo);
    }

    [Fact(DisplayName = "Deve falhar quando tipo de pacote não for encontrado")]
    public async Task Deve_Falhar_Quando_TipoPacote_Nao_Encontrado()
    {
        // Arrange
        var id = Guid.NewGuid();
        _repoMock.Setup(r => r.GetByIdAsync(id)).ReturnsAsync((TipoPacote?)null);

        var query = new GetTipoPacoteByIdQuery(id);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Tipo de pacote não encontrado", result.Messages!);
    }
}
