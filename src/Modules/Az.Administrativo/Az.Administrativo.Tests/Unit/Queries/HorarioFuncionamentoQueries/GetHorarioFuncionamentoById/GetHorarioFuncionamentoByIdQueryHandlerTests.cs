using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Queries.HorarioFuncionamentoQueries.GetHorarioFuncionamentoById;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;

public class GetHorarioFuncionamentoByIdQueryHandlerTests
{
    private readonly Mock<IHorarioFuncionamentoRepository> _repoMock = new();
    private readonly GetHorarioFuncionamentoByIdQueryHandler _handler;

    public GetHorarioFuncionamentoByIdQueryHandlerTests()
    {
        _handler = new GetHorarioFuncionamentoByIdQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar horário de funcionamento por id com sucesso")]
    public async Task Deve_Retornar_Horario_Por_Id_Com_Sucesso()
    {
        // Arrange
        var horario = new HorarioFuncionamento(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(horario);
        var query = new GetHorarioFuncionamentoByIdQuery(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(DayOfWeek.Monday, result.Data!.DiaSemana);
    }

    [Fact(DisplayName = "Deve retornar erro se horário não encontrado")]
    public async Task Deve_Retornar_Erro_Se_Nao_Encontrado()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync((HorarioFuncionamento)null!);
        var query = new GetHorarioFuncionamentoByIdQuery(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Horário de funcionamento não encontrado", result.Messages!);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro inesperado")]
    public async Task Deve_Lancar_Excecao_Se_Erro()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ThrowsAsync(new Exception("Erro de infra"));
        var query = new GetHorarioFuncionamentoByIdQuery(Guid.NewGuid());

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
    }
}