using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Queries.HorarioFuncionamentoQueries.GetAllHorariosFuncionamento;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;

public class GetAllHorariosFuncionamentoQueryHandlerTests
{
    private readonly Mock<IHorarioFuncionamentoRepository> _repoMock = new();
    private readonly GetAllHorariosFuncionamentoQueryHandler _handler;

    public GetAllHorariosFuncionamentoQueryHandlerTests()
    {
        _handler = new GetAllHorariosFuncionamentoQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar lista de horários de funcionamento")]
    public async Task Deve_Retornar_Lista_De_Horarios()
    {
        // Arrange
        var horarios = new List<HorarioFuncionamento>
        {
            new HorarioFuncionamento(DayOfWeek.Monday, new TimeOnly(8,0), new TimeOnly(18,0)),
            new HorarioFuncionamento(DayOfWeek.Tuesday, new TimeOnly(9,0), new TimeOnly(17,0))
        };
        _repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(horarios);
        var query = new GetAllHorariosFuncionamentoQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.Data!.Count);
        Assert.Equal(DayOfWeek.Monday, result.Data[0].DiaSemana);
        Assert.Equal(DayOfWeek.Tuesday, result.Data[1].DiaSemana);
    }

    [Fact(DisplayName = "Deve retornar lista vazia se não houver horários")]
    public async Task Deve_Retornar_Lista_Vazia()
    {
        // Arrange
        _repoMock.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<HorarioFuncionamento>());
        var query = new GetAllHorariosFuncionamentoQuery();

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Empty(result.Data!);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro inesperado")]
    public async Task Deve_Lancar_Excecao_Se_Erro()
    {
        // Arrange
        _repoMock.Setup(r => r.GetAllAsync()).ThrowsAsync(new Exception("Erro de infra"));
        var query = new GetAllHorariosFuncionamentoQuery();

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(query, CancellationToken.None));
    }
}