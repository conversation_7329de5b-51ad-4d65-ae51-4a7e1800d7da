using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Queries.PesoQueries.GetPesoById;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;

namespace Az.Administrativo.Tests.Unit.Queries.PesoQueries.GetPesoById;

public class GetPesoByIdQueryHandlerTests
{
    private readonly Mock<IPesoRepository> _repoMock;
    private readonly GetPesoByIdQueryHandler _handler;

    public GetPesoByIdQueryHandlerTests()
    {
        _repoMock = new Mock<IPesoRepository>();
        _handler = new GetPesoByIdQueryHandler(_repoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar peso por id com sucesso")]
    public async Task Deve_Retornar_Peso_Por_Id_Com_Sucesso()
    {
        // Arrange
        var pesoId = Guid.NewGuid();
        var peso = new Peso(pesoId, "200g", "Peso de 200 gramas", 1);
        _repoMock.Setup(r => r.GetByIdAsync(pesoId)).ReturnsAsync(peso);
        var query = new GetPesoByIdQuery(pesoId);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(pesoId, result.Data.IdPeso);
        Assert.Equal("200g", result.Data.Nome);
        Assert.Equal("Peso de 200 gramas", result.Data.Descricao);
        Assert.Equal(1, result.Data.Ordem);
        Assert.True(result.Data.Ativo);
    }

    [Fact(DisplayName = "Deve retornar erro quando peso não existe")]
    public async Task Deve_Retornar_Erro_Quando_Peso_Nao_Existe()
    {
        // Arrange
        var pesoId = Guid.NewGuid();
        _repoMock.Setup(r => r.GetByIdAsync(pesoId)).ReturnsAsync((Peso)null!);
        var query = new GetPesoByIdQuery(pesoId);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Null(result.Data);
        Assert.NotNull(result.Messages);
        Assert.Contains("Peso não encontrado", result.Messages);
    }

    [Fact(DisplayName = "Deve chamar o repositório com o id correto")]
    public async Task Deve_Chamar_Repositorio_Com_Id_Correto()
    {
        // Arrange
        var pesoId = Guid.NewGuid();
        _repoMock.Setup(r => r.GetByIdAsync(pesoId)).ReturnsAsync((Peso)null!);
        var query = new GetPesoByIdQuery(pesoId);

        // Act
        await _handler.Handle(query, CancellationToken.None);

        // Assert
        _repoMock.Verify(r => r.GetByIdAsync(pesoId), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar peso com todas as propriedades corretas")]
    public async Task Deve_Retornar_Peso_Com_Todas_Propriedades_Corretas()
    {
        // Arrange
        var pesoId = Guid.Parse("D36D2A85-8851-40C2-B2DB-8DBD0C7F5473");
        var peso = new Peso(pesoId, "300g", "Peso de 300 gramas para refeições médias", 2);
        _repoMock.Setup(r => r.GetByIdAsync(pesoId)).ReturnsAsync(peso);
        var query = new GetPesoByIdQuery(pesoId);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(pesoId, result.Data.IdPeso);
        Assert.Equal("300g", result.Data.Nome);
        Assert.Equal("Peso de 300 gramas para refeições médias", result.Data.Descricao);
        Assert.Equal(2, result.Data.Ordem);
        Assert.True(result.Data.Ativo);
    }
}
