using System;
using Xunit;
using Az.Administrativo.Application.Queries.PesoQueries.GetPesoById;

namespace Az.Administrativo.Tests.Unit.Queries.PesoQueries.GetPesoById;

public class GetPesoByIdQueryValidatorTests
{
    private readonly GetPesoByIdQueryValidator _validator;

    public GetPesoByIdQueryValidatorTests()
    {
        _validator = new GetPesoByIdQueryValidator();
    }

    [Fact(DisplayName = "Deve ser válido quando id é fornecido")]
    public void Deve_Ser_Valido_Quando_Id_E_Fornecido()
    {
        // Arrange
        var query = new GetPesoByIdQuery(Guid.NewGuid());

        // Act
        var result = _validator.Validate(query);

        // Assert
        Assert.True(result.IsValid);
        Assert.Empty(result.Errors);
    }

    [Fact(DisplayName = "Deve ser inválido quando id é vazio")]
    public void Deve_Ser_Invalido_Quando_Id_E_Vazio()
    {
        // Arrange
        var query = new GetPesoByIdQuery(Guid.Empty);

        // Act
        var result = _validator.Validate(query);

        // Assert
        Assert.False(result.IsValid);
        Assert.Single(result.Errors);
        Assert.Equal("Id do peso é obrigatório", result.Errors[0].ErrorMessage);
    }

    [Theory(DisplayName = "Deve validar diferentes cenários de id")]
    [InlineData("00000000-0000-0000-0000-000000000000", false)] // Guid.Empty
    [InlineData("D36D2A85-8851-40C2-B2DB-8DBD0C7F5473", true)]  // Guid válido (200g)
    [InlineData("00149AA6-6F11-4395-B73F-66AB53F34251", true)]  // Guid válido (300g)
    [InlineData("7D75D436-2173-496A-9D2F-165519671084", true)]  // Guid válido (400g)
    public void Deve_Validar_Diferentes_Cenarios_De_Id(string guidString, bool esperado)
    {
        // Arrange
        var guid = Guid.Parse(guidString);
        var query = new GetPesoByIdQuery(guid);

        // Act
        var result = _validator.Validate(query);

        // Assert
        Assert.Equal(esperado, result.IsValid);
    }

    [Fact(DisplayName = "Deve ter mensagem de erro específica para id vazio")]
    public void Deve_Ter_Mensagem_De_Erro_Especifica_Para_Id_Vazio()
    {
        // Arrange
        var query = new GetPesoByIdQuery(Guid.Empty);

        // Act
        var result = _validator.Validate(query);

        // Assert
        Assert.False(result.IsValid);
        Assert.Single(result.Errors);
        Assert.Equal("IdPeso", result.Errors[0].PropertyName);
        Assert.Equal("Id do peso é obrigatório", result.Errors[0].ErrorMessage);
    }
}
