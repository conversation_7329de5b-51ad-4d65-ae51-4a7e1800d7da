using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using MassTransit;
using Az.Administrativo.Application.Commands.FeriadoCommands.CreateFeriado;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Administrativo.Application.Services.Interfaces;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;
using Az.Shared.Shared.Common;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.IntegrationEvents.Events;

public class CreateFeriadoCommandHandlerTests
{
    private readonly Mock<IFeriadoRepository> _repoMock;
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock;
    private readonly Mock<IPublishEndpoint> _publishMock;
    private readonly Mock<ICurrentUser> _userMock;
    private readonly Mock<ICacheInvalidationService> _cacheInvalidationServiceMock;
    private readonly CreateFeriadoCommandHandler _handler;

    public CreateFeriadoCommandHandlerTests()
    {
        _repoMock = new Mock<IFeriadoRepository>();
        _uowMock = new Mock<IUnitOfWork<AdministrativoDbContext>>();
        _publishMock = new Mock<IPublishEndpoint>();
        _userMock = new Mock<ICurrentUser>();
        _cacheInvalidationServiceMock = new Mock<ICacheInvalidationService>();
        _handler = new CreateFeriadoCommandHandler(_repoMock.Object, _uowMock.Object, _publishMock.Object, _userMock.Object, _cacheInvalidationServiceMock.Object);
    }

    [Fact(DisplayName = "Deve criar feriado com sucesso quando não existe para a data informada")]
    public async Task Deve_Criar_Feriado_Com_Sucesso()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByDateAsync(It.IsAny<DateOnly>())).ReturnsAsync((Feriado)null!);
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));
        _publishMock.Setup(p => p.Publish(It.IsAny<FeriadoChangedIntegrationEvent>(), It.IsAny<CancellationToken>())).Returns(Task.CompletedTask);
        var command = new CreateFeriadoCommand(new DateOnly(2024, 12, 25), "Natal");

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotEqual(Guid.Empty, result.Data);
        _repoMock.Verify(r => r.AddAsync(It.IsAny<Feriado>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
        _publishMock.Verify(p => p.Publish(It.IsAny<FeriadoChangedIntegrationEvent>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se já existir feriado para a data")]
    public async Task Deve_Retornar_Erro_Se_Feriado_Existente()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByDateAsync(It.IsAny<DateOnly>())).ReturnsAsync(new Feriado(new DateOnly(2024, 12, 25), "Natal"));
        var command = new CreateFeriadoCommand(new DateOnly(2024, 12, 25), "Natal");

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Já existe um feriado cadastrado para esta data.", result.Messages!);
        _repoMock.Verify(r => r.AddAsync(It.IsAny<Feriado>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
        _publishMock.Verify(p => p.Publish(It.IsAny<FeriadoChangedIntegrationEvent>(), It.IsAny<CancellationToken>()), Times.Never);
    }
}