using System;
using Xunit;
using Az.Administrativo.Application.Commands.FeriadoCommands.CreateFeriado;

public class CreateFeriadoCommandValidatorTests
{
    private readonly CreateFeriadoCommandValidator _validator = new();

    [Fact(DisplayName = "Deve ser inválido se data for default")]
    public void Deve_Ser_Invalido_Se_Data_Default()
    {
        var command = new CreateFeriadoCommand(default, "Natal");
        var result = _validator.Validate(command);
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == "Data");
    }

    [Fact(DisplayName = "Deve ser inválido se descrição for vazia")]
    public void Deve_Ser_Invalido_Se_Descricao_Vazia()
    {
        var command = new CreateFeriadoCommand(new DateOnly(2024, 12, 25), "");
        var result = _validator.Validate(command);
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == "Descricao");
    }

    [Fact(DisplayName = "Deve ser válido se todos os campos estiverem corretos")]
    public void Deve_Ser_Valido_Se_Tudo_Correto()
    {
        var command = new CreateFeriadoCommand(new DateOnly(2024, 12, 25), "Natal");
        var result = _validator.Validate(command);
        Assert.True(result.IsValid);
    }
}