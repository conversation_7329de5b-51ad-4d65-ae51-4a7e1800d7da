using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Commands.TermoCondicaoCommands.CreateTermoCondicao;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Administrativo.Domain.Entities;
using Az.Shared.Shared.IRepositories;

namespace Az.Administrativo.Tests.Unit.Commands.TermoCondicaoCommands.CreateTermoCondicao;

public class CreateTermoCondicaoCommandHandlerTests
{
    private readonly Mock<ITermoCondicaoRepository> _repoMock;
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock;
    private readonly Mock<CreateTermoCondicaoCommandValidator> _validatorMock;
    private readonly CreateTermoCondicaoCommandHandler _handler;

    public CreateTermoCondicaoCommandHandlerTests()
    {
        _repoMock = new Mock<ITermoCondicaoRepository>();
        _uowMock = new Mock<IUnitOfWork<AdministrativoDbContext>>();
        _validatorMock = new Mock<CreateTermoCondicaoCommandValidator>();
        _handler = new CreateTermoCondicaoCommandHandler(_repoMock.Object, _uowMock.Object);
    }

    [Fact(DisplayName = "Deve criar termo e condição com sucesso")]
    public async Task Deve_Criar_TermoCondicao_Com_Sucesso()
    {
        // Arrange
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));
        var command = new CreateTermoCondicaoCommand
        {
            Titulo = "Termos de Uso",
            Descricao = "Descrição dos termos de uso"
        };

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotEqual(Guid.Empty, result.Data);
        _repoMock.Verify(r => r.AddAsync(It.Is<TermoCondicao>(t => 
            t.Titulo == command.Titulo && 
            t.Descricao == command.Descricao)), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve verificar se TermoCondicao é criado com propriedades corretas")]
    public async Task Deve_Verificar_Propriedades_TermoCondicao_Criado()
    {
        // Arrange
        TermoCondicao termoCapturado = null!;
        _repoMock.Setup(r => r.AddAsync(It.IsAny<TermoCondicao>()))
            .Callback<TermoCondicao>(t => termoCapturado = t)
            .Returns(Task.CompletedTask);
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));
        var command = new CreateTermoCondicaoCommand
        {
            Titulo = "Termos de Uso",
            Descricao = "Descrição dos termos de uso"
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(termoCapturado);
        Assert.Equal(command.Titulo, termoCapturado.Titulo);
        Assert.Equal(command.Descricao, termoCapturado.Descricao);
        Assert.True(termoCapturado.Ativo);
        Assert.NotEqual(Guid.Empty, termoCapturado.IdTermoCondicao);
        Assert.True(termoCapturado.DataDeCriacao > DateTime.MinValue);
    }

    [Fact(DisplayName = "Deve lançar exceção se commit falhar")]
    public async Task Deve_Lancar_Excecao_Se_Commit_Falhar()
    {
        // Arrange
        _uowMock.Setup(u => u.CommitAsync()).ThrowsAsync(new InvalidOperationException("Erro ao salvar no banco"));
        var command = new CreateTermoCondicaoCommand
        {
            Titulo = "Termos de Uso",
            Descricao = "Descrição dos termos de uso"
        };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro ao adicionar")]
    public async Task Deve_Lancar_Excecao_Se_Erro_Ao_Adicionar()
    {
        // Arrange
        _repoMock.Setup(r => r.AddAsync(It.IsAny<TermoCondicao>())).ThrowsAsync(new Exception("Erro de teste"));
        var command = new CreateTermoCondicaoCommand
        {
            Titulo = "Termos de Uso",
            Descricao = "Descrição dos termos de uso"
        };

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve tratar exceção e retornar erro se validator falhar")]
    public async Task Deve_Tratar_Excecao_Se_Validator_Falhar()
    {
        // Este teste é conceitual, dependendo de como a validação está implementada
        // Se a validação ocorrer antes do handler (comportamento típico com MediatR pipeline),
        // este teste pode ser considerado fora do escopo do handler

        // Arrange - Supondo que o handler trata validação internamente (menos comum)
        var validator = new CreateTermoCondicaoCommandValidator();
        var command = new CreateTermoCondicaoCommand(); // Comando vazio para falhar validação

        // Act & Assert - Confirma que a validação falharia neste caso
        var validationResult = await validator.ValidateAsync(command);
        Assert.False(validationResult.IsValid);
    }

    [Fact(DisplayName = "Deve lidar com cancelamento da operação")]
    public async Task Deve_Lidar_Com_Cancelamento()
    {
        // Arrange
        var command = new CreateTermoCondicaoCommand
        {
            Titulo = "Termos de Uso",
            Descricao = "Descrição dos termos de uso"
        };
        var cancellationTokenSource = new CancellationTokenSource();
        await cancellationTokenSource.CancelAsync(); // Cancela a operação

        // Act & Assert
        var result = await _handler.Handle(command, cancellationTokenSource.Token);
        Assert.False(result.Success);
        Assert.Contains("Operação cancelada.", result.Messages!);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro")]
    public async Task Deve_Lancar_Excecao_Se_Erro()
    {
        // Arrange
        _repoMock.Setup(r => r.AddAsync(It.IsAny<TermoCondicao>())).ThrowsAsync(new Exception("Erro de teste"));
        var command = new CreateTermoCondicaoCommand
        {
            Titulo = "Termos de Uso",
            Descricao = "Descrição dos termos de uso"
        };

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }
}
