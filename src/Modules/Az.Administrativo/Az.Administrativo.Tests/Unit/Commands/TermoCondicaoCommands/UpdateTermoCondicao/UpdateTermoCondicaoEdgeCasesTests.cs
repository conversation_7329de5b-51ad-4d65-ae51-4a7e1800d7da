using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Administrativo.Application.Commands.TermoCondicaoCommands.UpdateTermoCondicao;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Administrativo.Domain.Entities;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;

namespace Az.Administrativo.Tests.Unit.Commands.TermoCondicaoCommands.UpdateTermoCondicao;

public class UpdateTermoCondicaoEdgeCasesTests
{
    private readonly Mock<ITermoCondicaoRepository> _repoMock;
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock;
    private readonly Mock<ICurrentUser> _currentUserMock;
    private readonly UpdateTermoCondicaoCommandHandler _handler;

    public UpdateTermoCondicaoEdgeCasesTests()
    {
        _repoMock = new Mock<ITermoCondicaoRepository>();
        _uowMock = new Mock<IUnitOfWork<AdministrativoDbContext>>();
        _currentUserMock = new Mock<ICurrentUser>();
        _handler = new UpdateTermoCondicaoCommandHandler(_repoMock.Object, _uowMock.Object, _currentUserMock.Object);
    }
    
    [Fact(DisplayName = "Deve manter mesmos valores se não houver alterações")]
    public async Task Deve_Manter_Mesmos_Valores_Se_Nao_Houver_Alteracoes()
    {
        // Arrange
        var originalTitulo = "Título Original";
        var originalDescricao = "Descrição Original";
        var termoCondicao = new TermoCondicao(originalTitulo, originalDescricao);
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(termoCondicao);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());

        var command = new UpdateTermoCondicaoCommand
        {
            IdTermoCondicao = Guid.NewGuid(),
            Titulo = originalTitulo,  // Mesmo título
            Descricao = originalDescricao // Mesma descrição
        };

        TermoCondicao termoCapturado = null!;
        _repoMock.Setup(r => r.UpdateAsync(It.IsAny<TermoCondicao>()))
            .Callback<TermoCondicao>(t => termoCapturado = t)
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(termoCapturado);
        Assert.Equal(originalTitulo, termoCapturado.Titulo); // Verifica se manteve o mesmo
        Assert.Equal(originalDescricao, termoCapturado.Descricao); // Verifica se manteve o mesmo
    }

    [Fact(DisplayName = "Deve lidar com caracteres especiais na atualização")]
    public async Task Deve_Lidar_Com_Caracteres_Especiais()
    {
        // Arrange
        var termoCondicao = new TermoCondicao("Título Original", "Descrição Original");
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(termoCondicao);
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());

        var tituloComEspeciais = "Título Com Caracteres Especiais: !@#$%^&*()";
        var descricaoComEspeciais = "Descrição com símbolos: €£¥₩฿₽₹₴₱";

        var command = new UpdateTermoCondicaoCommand
        {
            IdTermoCondicao = Guid.NewGuid(),
            Titulo = tituloComEspeciais,
            Descricao = descricaoComEspeciais
        };

        TermoCondicao termoCapturado = null!;
        _repoMock.Setup(r => r.UpdateAsync(It.IsAny<TermoCondicao>()))
            .Callback<TermoCondicao>(t => termoCapturado = t)
            .Returns(Task.CompletedTask);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(termoCapturado);
        Assert.Equal(tituloComEspeciais, termoCapturado.Titulo);
        Assert.Equal(descricaoComEspeciais, termoCapturado.Descricao);
    }
}
