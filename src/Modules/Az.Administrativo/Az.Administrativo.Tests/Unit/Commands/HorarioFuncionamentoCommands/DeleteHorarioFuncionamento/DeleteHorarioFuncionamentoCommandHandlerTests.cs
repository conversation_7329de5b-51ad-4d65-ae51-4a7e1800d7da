using Az.Administrativo.Application.Commands.HorarioFuncionamentoCommands.DeleteHorarioFuncionamento;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;
using Moq;

using Az.Administrativo.Application.Services.Interfaces;
namespace Az.Administrativo.Tests.Unit.Commands.HorarioFuncionamentoCommands.DeleteHorarioFuncionamento;

public class DeleteHorarioFuncionamentoCommandHandlerTests
{
    private readonly Mock<IHorarioFuncionamentoRepository> _repoMock = new();
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock = new();
    private readonly Mock<ICurrentUser> _userMock = new();
    private readonly Mock<ICacheInvalidationService> _cacheInvalidationServiceMock = new();
    private readonly DeleteHorarioFuncionamentoCommandHandler _handler;

    public DeleteHorarioFuncionamentoCommandHandlerTests()
    {
        _handler = new DeleteHorarioFuncionamentoCommandHandler(_repoMock.Object, _uowMock.Object, _userMock.Object, _cacheInvalidationServiceMock.Object);
    }

    [Fact(DisplayName = "Deve excluir horário de funcionamento com sucesso")]
    public async Task Deve_Excluir_Horario_Com_Sucesso()
    {
        // Arrange
        var horario = new HorarioFuncionamento(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(horario);
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));
        _userMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        var command = new DeleteHorarioFuncionamentoCommand(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        _repoMock.Verify(r => r.DeleteAsync(horario, It.IsAny<Guid>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se horário não encontrado")]
    public async Task Deve_Retornar_Erro_Se_Horario_Nao_Encontrado()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync((HorarioFuncionamento)null!);
        var command = new DeleteHorarioFuncionamentoCommand(Guid.NewGuid());

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Horário de funcionamento não encontrado", result.Messages!);
        _repoMock.Verify(r => r.DeleteAsync(It.IsAny<HorarioFuncionamento>(), It.IsAny<Guid>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve lançar exceção se UserId for nulo ou inválido")]
    public async Task Deve_Lancar_Excecao_Se_UserId_Nulo_Ou_Invalido()
    {
        // Arrange
        var horario = new HorarioFuncionamento(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(horario);
        _userMock.Setup(u => u.UserId).Returns((string?)null);
        var command = new DeleteHorarioFuncionamentoCommand(Guid.NewGuid());

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact(DisplayName = "Deve retornar erro se ocorrer exceção inesperada")]
    public async Task Deve_Retornar_Erro_Se_Excecao()
    {
        // Arrange
        var horario = new HorarioFuncionamento(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(horario);
        _userMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        _repoMock.Setup(r => r.DeleteAsync(It.IsAny<HorarioFuncionamento>(), It.IsAny<Guid>())).ThrowsAsync(new Exception("Erro de infra"));
        var command = new DeleteHorarioFuncionamentoCommand(Guid.NewGuid());

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }
}