using Az.Administrativo.Application.Commands.HorarioFuncionamentoCommands.UpdateHorarioFuncionamento;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;
using Az.Administrativo.Application.Services.Interfaces;
using Moq;

namespace Az.Administrativo.Tests.Unit.Commands.HorarioFuncionamentoCommands.UpdateHorarioFuncionamento;

public class UpdateHorarioFuncionamentoCommandHandlerTests
{
    private readonly Mock<IHorarioFuncionamentoRepository> _repoMock = new();
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock = new();
    private readonly Mock<ICurrentUser> _currentUserMock = new();
    private readonly Mock<ICacheInvalidationService> _cacheInvalidationServiceMock = new();
    private readonly UpdateHorarioFuncionamentoCommandHandler _handler;

    public UpdateHorarioFuncionamentoCommandHandlerTests()
    {
        _handler = new UpdateHorarioFuncionamentoCommandHandler(_repoMock.Object, _uowMock.Object, _currentUserMock.Object, _cacheInvalidationServiceMock.Object);
    }

    [Fact(DisplayName = "Deve atualizar horário de funcionamento com sucesso")]
    public async Task Deve_Atualizar_Horario_Com_Sucesso()
    {
        // Arrange
        var horario = new HorarioFuncionamento(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(horario);
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        var command = new UpdateHorarioFuncionamentoCommand(Guid.NewGuid(), DayOfWeek.Monday, new TimeOnly(9, 0), new TimeOnly(19, 0));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.True(result.Data);
        Assert.Null(result.Messages);
        _repoMock.Verify(r => r.UpdateAsync(It.IsAny<HorarioFuncionamento>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se horário não encontrado")]
    public async Task Deve_Retornar_Erro_Se_Horario_Nao_Encontrado()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync((HorarioFuncionamento)null!);
        var command = new UpdateHorarioFuncionamentoCommand(Guid.NewGuid(), DayOfWeek.Monday, new TimeOnly(9, 0), new TimeOnly(19, 0));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.False(result.Data);
        Assert.NotNull(result.Messages);
        Assert.Contains("Horário de funcionamento não encontrado", result.Messages!);
        _repoMock.Verify(r => r.UpdateAsync(It.IsAny<HorarioFuncionamento>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve retornar erro se ocorrer exceção inesperada")]
    public async Task Deve_Retornar_Erro_Se_Excecao()
    {
        // Arrange
        var horario = new HorarioFuncionamento(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(horario);
        _repoMock.Setup(r => r.UpdateAsync(It.IsAny<HorarioFuncionamento>())).ThrowsAsync(new Exception("Erro de infra"));
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        var command = new UpdateHorarioFuncionamentoCommand(Guid.NewGuid(), DayOfWeek.Monday, new TimeOnly(9, 0), new TimeOnly(19, 0));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }
}