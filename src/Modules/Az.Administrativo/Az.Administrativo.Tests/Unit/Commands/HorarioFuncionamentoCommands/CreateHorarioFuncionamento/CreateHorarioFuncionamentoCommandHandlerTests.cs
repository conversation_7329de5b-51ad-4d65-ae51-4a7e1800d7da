using Az.Administrativo.Application.Commands.HorarioFuncionamentoCommands.CreateHorarioFuncionamento;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.IRepositories;
using Az.Administrativo.Application.Services.Interfaces;
using Moq;

namespace Az.Administrativo.Tests.Unit.Commands.HorarioFuncionamentoCommands.CreateHorarioFuncionamento;

public class CreateHorarioFuncionamentoCommandHandlerTests
{
    private readonly Mock<IHorarioFuncionamentoRepository> _repoMock = new();
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock = new();
    private readonly Mock<ICacheInvalidationService> _cacheInvalidationServiceMock = new();
    private readonly CreateHorarioFuncionamentoCommandHandler _handler;

    public CreateHorarioFuncionamentoCommandHandlerTests()
    {
        _handler = new CreateHorarioFuncionamentoCommandHandler(_repoMock.Object, _uowMock.Object, _cacheInvalidationServiceMock.Object);
    }

    [Fact(DisplayName = "Deve criar horário de funcionamento com sucesso")]
    public async Task Deve_Criar_Horario_Com_Sucesso()
    {
        // Arrange
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));
        var command = new CreateHorarioFuncionamentoCommand(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        _repoMock.Verify(r => r.AddAsync(It.IsAny<HorarioFuncionamento>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se ocorrer exceção inesperada")]
    public async Task Deve_Retornar_Erro_Se_Excecao()
    {
        // Arrange
        _repoMock.Setup(r => r.AddAsync(It.IsAny<HorarioFuncionamento>())).ThrowsAsync(new Exception("Erro de infra"));
        var command = new CreateHorarioFuncionamentoCommand(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }
}