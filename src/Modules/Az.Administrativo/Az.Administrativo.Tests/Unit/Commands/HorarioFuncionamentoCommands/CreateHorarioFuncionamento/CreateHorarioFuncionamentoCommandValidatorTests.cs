using Az.Administrativo.Application.Commands.HorarioFuncionamentoCommands.CreateHorarioFuncionamento;

namespace Az.Administrativo.Tests.Unit.Commands.HorarioFuncionamentoCommands.CreateHorarioFuncionamento;

public class CreateHorarioFuncionamentoCommandValidatorTests
{
    private readonly CreateHorarioFuncionamentoCommandValidator _validator = new();

    [Fact(DisplayName = "Deve ser inválido se hora de abertura for nula")]
    public void Deve_Ser_Invalido_Se_HoraAbertura_Nula()
    {
        var command = new CreateHorarioFuncionamentoCommand(DayOfWeek.Monday, default, new TimeOnly(18, 0));
        var result = _validator.Validate(command);
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == "HoraAbertura");
        Assert.Contains(result.Errors, e => e.ErrorMessage.Contains("Hora de abertura é obrigatória"));
    }

    [Fact(DisplayName = "Deve ser inválido se hora de fechamento for nula")]
    public void Deve_Ser_Invalido_Se_HoraFechamento_Nula()
    {
        var command = new CreateHorarioFuncionamentoCommand(DayOfWeek.Monday, new TimeOnly(8, 0), default);
        var result = _validator.Validate(command);
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == "HoraFechamento");
        Assert.Contains(result.Errors, e => e.ErrorMessage.Contains("Hora de fechamento é obrigatória"));
    }

    [Fact(DisplayName = "Deve ser inválido se hora de abertura não for menor que fechamento")]
    public void Deve_Ser_Invalido_Se_HoraAbertura_Nao_Menor_Que_Fechamento()
    {
        var command = new CreateHorarioFuncionamentoCommand(DayOfWeek.Monday, new TimeOnly(18, 0), new TimeOnly(8, 0));
        var result = _validator.Validate(command);
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.ErrorMessage.Contains("menor que a de fechamento"));
    }

    [Fact(DisplayName = "Deve ser inválido se hora de abertura e fechamento forem iguais")]
    public void Deve_Ser_Invalido_Se_Horas_Iguais()
    {
        var command = new CreateHorarioFuncionamentoCommand(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(8, 0));
        var result = _validator.Validate(command);
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.ErrorMessage.Contains("não podem ser iguais"));
    }

    [Fact(DisplayName = "Deve ser inválido se dia da semana for inválido")]
    public void Deve_Ser_Invalido_Se_DiaSemana_Invalido()
    {
        var command = new CreateHorarioFuncionamentoCommand((DayOfWeek)99, new TimeOnly(8, 0), new TimeOnly(18, 0));
        var result = _validator.Validate(command);
        Assert.False(result.IsValid);
        Assert.Contains(result.Errors, e => e.PropertyName == "DiaSemana");
    }

    [Fact(DisplayName = "Deve ser válido se todos os campos estiverem corretos")]
    public void Deve_Ser_Valido_Se_Tudo_Correto()
    {
        var command = new CreateHorarioFuncionamentoCommand(DayOfWeek.Monday, new TimeOnly(8, 0), new TimeOnly(18, 0));
        var result = _validator.Validate(command);
        Assert.True(result.IsValid);
    }
}