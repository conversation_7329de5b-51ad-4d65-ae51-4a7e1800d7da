using Xunit;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Az.Administrativo.Application.Commands.FormaPagamentoCommands.DeleteFormaPagamento;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.Enums;
using System;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;

public class DeleteFormaPagamentoCommandHandlerTests
{
    private readonly Mock<IFormaPagamentoRepository> _repoMock;
    private readonly DeleteFormaPagamentoCommandHandler _handler;
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock;
    private readonly Mock<ICurrentUser> _currentUserMock;

    public DeleteFormaPagamentoCommandHandlerTests()
    {
        _repoMock = new Mock<IFormaPagamentoRepository>();
        _uowMock = new Mock<IUnitOfWork<AdministrativoDbContext>>();
        _currentUserMock = new Mock<ICurrentUser>();
        _handler = new DeleteFormaPagamentoCommandHandler(_repoMock.Object, _uowMock.Object, _currentUserMock.Object);
    }

    [Fact(DisplayName = "Deve excluir (soft delete) forma de pagamento com sucesso")]
    public async Task Deve_Excluir_FormaPagamento_Com_Sucesso()
    {
        // Arrange
        var forma = new FormaPagamento("Cartão", TipoFormaPagamentoEnum.CartaoCreditoAVista);
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(forma);
        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        
        // Act
        var command = new DeleteFormaPagamentoCommand(Guid.NewGuid());
        var result = await _handler.Handle(command, CancellationToken.None);
       
        // Assert
        Assert.True(result.Success);
        _repoMock.Verify(r => r.UpdateAsync(forma), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se forma de pagamento não encontrada")]
    public async Task Deve_Retornar_Erro_Se_Nao_Encontrado()
    {
        // Arrange
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync((FormaPagamento?)null);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);
        
        // Act
        var command = new DeleteFormaPagamentoCommand(Guid.NewGuid());
        var result = await _handler.Handle(command, CancellationToken.None);
        
        // Assert
        Assert.False(result.Success);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro no repositório")]
    public async Task Deve_Lancar_Excecao_Se_Erro_No_Repositorio()
    {
        // Arrange
        var forma = new FormaPagamento("Cartão", TipoFormaPagamentoEnum.CartaoCreditoAVista);
        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        _repoMock.Setup(r => r.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(forma);
        _repoMock.Setup(r => r.UpdateAsync(It.IsAny<FormaPagamento>())).ThrowsAsync(new Exception("Erro de infra"));
        
        // Act & Assert
        var command = new DeleteFormaPagamentoCommand(Guid.NewGuid());
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }
}