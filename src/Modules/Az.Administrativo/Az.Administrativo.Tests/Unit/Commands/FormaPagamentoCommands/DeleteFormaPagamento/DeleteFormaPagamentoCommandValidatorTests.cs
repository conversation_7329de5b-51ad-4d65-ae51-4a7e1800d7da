using Xunit;
using Az.Administrativo.Application.Commands.FormaPagamentoCommands.DeleteFormaPagamento;

public class DeleteFormaPagamentoCommandValidatorTests
{
    [Fact]
    public void Deve_Validar_Id_Valido()
    {
        var validator = new DeleteFormaPagamentoCommandValidator();
        var command = new DeleteFormaPagamentoCommand(Guid.NewGuid());
        var result = validator.Validate(command);
        Assert.True(result.IsValid);
    }

    [Fact]
    public void Deve_Falhar_Se_Id_Vazio()
    {
        var validator = new DeleteFormaPagamentoCommandValidator();
        var command = new DeleteFormaPagamentoCommand(Guid.Empty);
        var result = validator.Validate(command);
        Assert.False(result.IsValid);
    }
}