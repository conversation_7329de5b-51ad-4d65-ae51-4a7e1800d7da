using Xunit;
using System;
using FluentValidation.TestHelper;
using Az.Administrativo.Application.Commands.FormaPagamentoCommands.CreateFormaPagamento;
using Az.Administrativo.Domain.Enums;

namespace Az.Administrativo.Tests.Unit.Commands.FormaPagamentoCommands.CreateFormaPagamento;

public class CreateFormaPagamentoCommandValidatorTests
{
    private readonly CreateFormaPagamentoCommandValidator _validator;

    public CreateFormaPagamentoCommandValidatorTests()
    {
        _validator = new CreateFormaPagamentoCommandValidator();
    }

    [Fact(DisplayName = "Deve validar comando válido")]
    public void Deve_Validar_Comando_Valido()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Pix", TipoFormaPagamentoEnum.Pix, null, null, null);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory(DisplayName = "Deve validar comando com todos os campos preenchidos")]
    [InlineData("Cartão de Crédito Parcelado", 3, 12, "Observação teste", 1.5)] // TipoFormaPagamentoEnum.CartaoCreditoParcelado
    [InlineData("Cartão de Débito", 4, null, null, null)] // TipoFormaPagamentoEnum.CartaoDebito
    [InlineData("Dinheiro", 5, null, null, null)] // TipoFormaPagamentoEnum.Dinheiro
    [InlineData("Pix", 1, null, null, null)] // TipoFormaPagamentoEnum.Pix
    public void Deve_Validar_Comando_Com_Todos_Campos(string nome, int tipoInt, int? maxParcelas, string? observacao, double? acrescimoDouble)
    {
        // Arrange
        var tipo = (TipoFormaPagamentoEnum)tipoInt;
        var acrescimo = acrescimoDouble.HasValue ? (decimal?)Convert.ToDecimal(acrescimoDouble.Value) : null;
        var command = new CreateFormaPagamentoCommand(nome, tipo, maxParcelas, observacao, acrescimo);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory(DisplayName = "Deve falhar se nome for inválido")]
    [InlineData("", "Nome não pode ser vazio")]
    public void Deve_Falhar_Se_Nome_Invalido(string nome, string mensagemEsperada)
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand(nome, TipoFormaPagamentoEnum.Pix, null, null, null);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(c => c.Nome)
              .WithErrorMessage(mensagemEsperada);
    }

    [Fact(DisplayName = "Deve falhar se nome for muito longo")]
    public void Deve_Falhar_Se_Nome_Muito_Longo()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand(new string('A', 101), TipoFormaPagamentoEnum.Pix, null, null, null);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(c => c.Nome)
              .WithErrorMessage("Nome não pode exceder 100 caracteres");
    }

    [Fact(DisplayName = "Deve falhar se valor de acréscimo for negativo")]
    public void Deve_Falhar_Se_Acrescimo_Negativo()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Cartão de Crédito", TipoFormaPagamentoEnum.CartaoCreditoParcelado, 12, null, -0.1m);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(c => c.Acrescimo)
              .WithErrorMessage("Acréscimo não pode ser negativo");
    }

    [Fact(DisplayName = "Deve falhar se máximo parcelas for negativo")]
    public void Deve_Falhar_Se_MaximoParcelas_Negativo()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Cartão de Crédito", TipoFormaPagamentoEnum.CartaoCreditoParcelado, -1, null, 1.5m);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(c => c.MaximoParcelas)
              .WithErrorMessage("Máximo de parcelas não pode ser negativo");
    }

    [Fact(DisplayName = "Deve falhar se observação for muito longa")]
    public void Deve_Falhar_Se_Observacao_Muito_Longa()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Pix", TipoFormaPagamentoEnum.Pix, null, new string('A', 501), null);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(c => c.Observacao)
              .WithErrorMessage("Observação não pode exceder 500 caracteres");
    }

    [Fact(DisplayName = "Deve validar se observação for vazia")]
    public void Deve_Validar_Se_Observacao_Vazia()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Pix", TipoFormaPagamentoEnum.Pix, null, "", null);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(c => c.Observacao);
    }

    [Fact(DisplayName = "Deve validar se máximo parcelas for nulo e não for cartão de crédito parcelado")]
    public void Deve_Validar_Se_MaximoParcelas_Nulo_Sem_Ser_Cartao_Parcelado()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Pix", TipoFormaPagamentoEnum.Pix, null, null, null);

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(c => c.MaximoParcelas);
    }
}