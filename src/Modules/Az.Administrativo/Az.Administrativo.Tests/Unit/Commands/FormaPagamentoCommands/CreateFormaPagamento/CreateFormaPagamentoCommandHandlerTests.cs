using Xunit;
using Moq;
using System;
using System.Threading;
using System.Threading.Tasks;
using Az.Administrativo.Application.Commands.FormaPagamentoCommands.CreateFormaPagamento;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.Enums;
using Az.Administrativo.Infrastructure.Context;
using Az.Shared.Shared.IRepositories;

namespace Az.Administrativo.Tests.Unit.Commands.FormaPagamentoCommands.CreateFormaPagamento;

public class CreateFormaPagamentoCommandHandlerTests
{
    private readonly Mock<IFormaPagamentoRepository> _repoMock;
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock;
    private readonly CreateFormaPagamentoCommandHandler _handler;

    public CreateFormaPagamentoCommandHandlerTests()
    {
        _repoMock = new Mock<IFormaPagamentoRepository>();
        _uowMock = new Mock<IUnitOfWork<AdministrativoDbContext>>();
        _handler = new CreateFormaPagamentoCommandHandler(_repoMock.Object, _uowMock.Object);
    }

    [Fact(DisplayName = "Deve criar forma de pagamento com sucesso")]
    public async Task Deve_Criar_FormaPagamento_Com_Sucesso()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Cartão de Crédito", TipoFormaPagamentoEnum.CartaoCreditoParcelado, 12, "Aceita Visa", 2.5m);
        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotEqual(Guid.Empty, result.Data);
        _repoMock.Verify(r => r.AddAsync(It.IsAny<FormaPagamento>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve verificar propriedades da forma de pagamento criada")]
    public async Task Deve_Verificar_Propriedades_FormaPagamento_Criada()
    {
        // Arrange
        FormaPagamento formaPagamentoCapturada = null!;
        _repoMock.Setup(r => r.AddAsync(It.IsAny<FormaPagamento>()))
                 .Callback<FormaPagamento>(f => formaPagamentoCapturada = f)
                 .Returns(Task.CompletedTask);
        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);

        var command = new CreateFormaPagamentoCommand(
            "Cartão de Crédito", 
            TipoFormaPagamentoEnum.CartaoCreditoParcelado, 
            12, 
            "Aceita Visa", 
            2.5m
        );

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(formaPagamentoCapturada);
        Assert.Equal(command.Nome, formaPagamentoCapturada.Nome);
        Assert.Equal(command.Tipo, formaPagamentoCapturada.Tipo);
        Assert.Equal(command.MaximoParcelas, formaPagamentoCapturada.MaximoParcelas);
        Assert.Equal(command.Observacao, formaPagamentoCapturada.Observacao);
        Assert.Equal(command.Acrescimo, formaPagamentoCapturada.Acrescimo);
        Assert.NotEqual(Guid.Empty, formaPagamentoCapturada.IdFormaPagamento);
        Assert.True(formaPagamentoCapturada.Ativo);
    }

    [Fact(DisplayName = "Deve criar forma de pagamento sem campos opcionais")]
    public async Task Deve_Criar_FormaPagamento_Sem_Campos_Opcionais()
    {
        // Arrange
        FormaPagamento formaPagamentoCapturada = null!;
        _repoMock.Setup(r => r.AddAsync(It.IsAny<FormaPagamento>()))
                 .Callback<FormaPagamento>(f => formaPagamentoCapturada = f)
                 .Returns(Task.CompletedTask);
        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);

        var command = new CreateFormaPagamentoCommand(
            "Pix", 
            TipoFormaPagamentoEnum.Pix, 
            null, 
            null, 
            null
        );

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(formaPagamentoCapturada);
        Assert.Equal(command.Nome, formaPagamentoCapturada.Nome);
        Assert.Equal(command.Tipo, formaPagamentoCapturada.Tipo);
        Assert.Null(formaPagamentoCapturada.MaximoParcelas);
        Assert.Null(formaPagamentoCapturada.Observacao);
        Assert.Null(formaPagamentoCapturada.Acrescimo);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro no repositório")]
    public async Task Deve_Lancar_Excecao_Se_Erro_No_Repositorio()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Cartão de Crédito", TipoFormaPagamentoEnum.CartaoCreditoParcelado, 12, "Aceita Visa", 2.5m);
        _repoMock.Setup(r => r.AddAsync(It.IsAny<FormaPagamento>())).ThrowsAsync(new Exception("Erro de infra"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve lançar exceção se commit falhar")]
    public async Task Deve_Lancar_Excecao_Se_Commit_Falhar()
    {
        // Arrange
        var command = new CreateFormaPagamentoCommand("Cartão de Crédito", TipoFormaPagamentoEnum.CartaoCreditoParcelado, 12, "Aceita Visa", 2.5m);
        _uowMock.Setup(u => u.CommitAsync()).ThrowsAsync(new Exception("Erro ao salvar no banco"));

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }

}