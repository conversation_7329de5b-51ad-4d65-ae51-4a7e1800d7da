using Xunit;
using Moq;
using System.Threading;
using System.Threading.Tasks;
using Az.Administrativo.Application.Commands.FormaPagamentoCommands.UpdateFormaPagamento;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.Enums;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;

namespace Az.Administrativo.Tests.Unit.Commands.FormaPagamentoCommands.UpdateFormaPagamento;

public class UpdateFormaPagamentoCommandHandlerTests
{
    private readonly Mock<IFormaPagamentoRepository> _repoMock;
    private readonly Mock<IUnitOfWork<AdministrativoDbContext>> _uowMock;
    private readonly Mock<ICurrentUser> _currentUserMock;
    private readonly UpdateFormaPagamentoCommandHandler _handler;

    public UpdateFormaPagamentoCommandHandlerTests()
    {
        _repoMock = new Mock<IFormaPagamentoRepository>();
        _uowMock = new Mock<IUnitOfWork<AdministrativoDbContext>>();
        _currentUserMock = new Mock<ICurrentUser>();
        _handler = new UpdateFormaPagamentoCommandHandler(_repoMock.Object, _uowMock.Object, _currentUserMock.Object);
    }

    [Fact(DisplayName = "Deve atualizar forma de pagamento com sucesso")]
    public async Task Deve_Atualizar_FormaPagamento_Com_Sucesso()
    {
        // Arrange
        var id = Guid.NewGuid();
        var formaPagamento = new FormaPagamento("Nome Original", TipoFormaPagamentoEnum.Pix);
        // Define o ID manualmente para combinar com o da command
        typeof(FormaPagamento).GetProperty("IdFormaPagamento")!.SetValue(formaPagamento, id);

        _repoMock.Setup(r => r.GetByIdAsync(id)).ReturnsAsync(formaPagamento);
        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());

        var command = new UpdateFormaPagamentoCommand
        (
            id,
            "Novo Nome",
            TipoFormaPagamentoEnum.CartaoCreditoParcelado,
            12,
            "Nova observação",
            3.5m
        );

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        _repoMock.Verify(r => r.UpdateAsync(It.IsAny<FormaPagamento>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve verificar propriedades atualizadas")]
    public async Task Deve_Verificar_Propriedades_Atualizadas()
    {
        // Arrange
        var id = Guid.NewGuid();
        var formaPagamento = new FormaPagamento("Nome Original", TipoFormaPagamentoEnum.Pix);
        // Define o ID manualmente
        typeof(FormaPagamento).GetProperty("IdFormaPagamento")!.SetValue(formaPagamento, id);

        _repoMock.Setup(r => r.GetByIdAsync(id)).ReturnsAsync(formaPagamento);

        FormaPagamento formaPagamentoCapturada = null!;
        _repoMock.Setup(r => r.UpdateAsync(It.IsAny<FormaPagamento>()))
                 .Callback<FormaPagamento>(f => formaPagamentoCapturada = f)
                 .Returns(Task.CompletedTask);

        _uowMock.Setup(u => u.CommitAsync()).ReturnsAsync(1);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
       
        var command = new UpdateFormaPagamentoCommand
        (
            id,
            "Novo Nome",
            TipoFormaPagamentoEnum.CartaoCreditoParcelado,
            12,
            "Nova observação",
            3.5m
        );

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(formaPagamentoCapturada);
        Assert.Equal(command.Nome, formaPagamentoCapturada.Nome);
        Assert.Equal(command.Tipo, formaPagamentoCapturada.Tipo);
        Assert.Equal(command.MaximoParcelas, formaPagamentoCapturada.MaximoParcelas);
        Assert.Equal(command.Observacao, formaPagamentoCapturada.Observacao);
        Assert.Equal(command.Acrescimo, formaPagamentoCapturada.Acrescimo);
    }

    [Fact(DisplayName = "Deve retornar erro quando forma de pagamento não existir")]
    public async Task Deve_Retornar_Erro_Quando_Nao_Existir()
    {
        // Arrange
        var id = Guid.NewGuid();
        _repoMock.Setup(r => r.GetByIdAsync(id)).ReturnsAsync((FormaPagamento)null!);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());

        var command = new UpdateFormaPagamentoCommand
        (
            id,
            "Novo Nome",
            TipoFormaPagamentoEnum.CartaoCreditoParcelado,
            12,
            "Nova observação",
            3.5m
        );
        
       
        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("não encontrada", result.Messages![0].ToLower());
        _repoMock.Verify(r => r.UpdateAsync(It.IsAny<FormaPagamento>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve lançar exceção se ocorrer erro no repositório")]
    public async Task Deve_Lancar_Excecao_Se_Erro_No_Repositorio()
    {
        // Arrange
        var id = Guid.NewGuid();
        var formaPagamento = new FormaPagamento("Nome Original", TipoFormaPagamentoEnum.Pix);
        // Define o ID manualmente
        typeof(FormaPagamento).GetProperty("IdFormaPagamento")!.SetValue(formaPagamento, id);

        _repoMock.Setup(r => r.GetByIdAsync(id)).ReturnsAsync(formaPagamento);
        _repoMock.Setup(r => r.UpdateAsync(It.IsAny<FormaPagamento>())).ThrowsAsync(new Exception("Erro de infra"));
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());

        var command = new UpdateFormaPagamentoCommand
        (
            id,
            "Novo Nome",
            TipoFormaPagamentoEnum.CartaoCreditoParcelado,
            null,
            null,
            null
        );

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve lançar exceção se commit falhar")]
    public async Task Deve_Lancar_Excecao_Se_Commit_Falhar()
    {
        // Arrange
        var id = Guid.NewGuid();
        var formaPagamento = new FormaPagamento("Nome Original", TipoFormaPagamentoEnum.Pix);
        // Define o ID manualmente
        typeof(FormaPagamento).GetProperty("IdFormaPagamento")!.SetValue(formaPagamento, id);

        _repoMock.Setup(r => r.GetByIdAsync(id)).ReturnsAsync(formaPagamento);
        _uowMock.Setup(u => u.CommitAsync()).ThrowsAsync(new Exception("Erro ao salvar no banco"));
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());

       var command = new UpdateFormaPagamentoCommand
        (
            id,
            "Novo Nome",
            TipoFormaPagamentoEnum.CartaoCreditoParcelado,
            null,
            null,
            null
        );

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
    }
}