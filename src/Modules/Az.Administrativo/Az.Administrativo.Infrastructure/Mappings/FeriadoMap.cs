using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Az.Administrativo.Domain.Entities;
using Az.Shared.Shared.Entities;

namespace Az.Administrativo.Infrastructure.Mappings;

public class FeriadoMap : BaseEntidadeMap<Feriado>
{
    public override void Configure(EntityTypeBuilder<Feriado> builder)
    {
        base.Configure(builder); // Mapeia campos herdados

        builder.ToTable("Feriado");
        builder.HasKey(f => f.IdFeriado);

        builder.Property(f => f.IdFeriado)
            .IsRequired();

        builder.Property(f => f.Data)
            .IsRequired();

        builder.Property(f => f.Des<PERSON>ricao)
            .IsRequired()
            .HasMaxLength(200);
    }
}