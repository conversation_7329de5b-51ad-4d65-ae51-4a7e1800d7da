using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Microsoft.EntityFrameworkCore;

namespace Az.Administrativo.Infrastructure.Repositories;

public class ProteinaRepository : Repository<Proteina, AdministrativoDbContext>, IProteinaRepository
{
    public ProteinaRepository(AdministrativoDbContext context) : base(context)
    {
    }

    public async Task<Proteina?> GetByDescricaoAsync(string descricao)
    {
        return await _dbSet
            .FirstOrDefaultAsync(p => p.Descricao == descricao && !p.Excluido);
    }
}
