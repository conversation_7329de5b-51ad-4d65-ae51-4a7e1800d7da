using Az.Administrativo.Domain.Entities;
using Az.Administrativo.Domain.IRepositories;
using Az.Administrativo.Infrastructure.Context;
using Microsoft.EntityFrameworkCore;

namespace Az.Administrativo.Infrastructure.Repositories;

public class TipoPacoteRepository : Repository<TipoPacote, AdministrativoDbContext>, ITipoPacoteRepository
{
    public TipoPacoteRepository(AdministrativoDbContext context) : base(context)
    {
    }

    public async Task<List<TipoPacote>> GetByOrdemAsync(int ordem)
    {
        return await _dbSet
            .Where(tp => tp.Ordem == ordem && !tp.Excluido)
            .ToListAsync();
    }

    public async Task<TipoPacote?> GetByDescricaoAsync(string descricao)
    {
        return await _dbSet
            .FirstOrDefaultAsync(tp => tp.Descricao == descricao && !tp.Excluido);
    }
}
