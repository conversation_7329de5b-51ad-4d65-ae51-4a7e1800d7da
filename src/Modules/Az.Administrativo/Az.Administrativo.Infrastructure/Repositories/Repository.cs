using Microsoft.EntityFrameworkCore;
using Az.Shared.Shared.Entities;
using Az.Shared.Shared.IRepositories;

namespace Az.Administrativo.Infrastructure.Repositories;

public class Repository<TEntity, TContext> : IRepository<TEntity>
    where TEntity : BaseEntidade
    where TContext : DbContext
{
    protected readonly TContext _context;
    protected readonly DbSet<TEntity> _dbSet;

    public Repository(TContext context)
    {
        _context = context;
        _dbSet = context.Set<TEntity>();
    }

    public virtual async Task<TEntity?> GetByIdAsync(Guid id)
        => await _dbSet.FindAsync(id);

    public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
        => await _dbSet.Where(e => !e.Excluido).ToListAsync();

    public virtual async Task<IEnumerable<TEntity>> GetAllActiveAsync()
        => await _dbSet.Where(e => e.Ativo && !e.Excluido).ToListAsync();

    public virtual async Task<IEnumerable<TEntity>> GetPagedAsync(int page, int pageSize)
        => await _dbSet.Where(e => !e.Excluido)
                        .Skip((page - 1) * pageSize)
                        .Take(pageSize)
                        .ToListAsync();

    public virtual async Task AddAsync(TEntity entity)
    {
        await _dbSet.AddAsync(entity);
        // Commit deve ser feito pelo UnitOfWork
    }

    public virtual Task UpdateAsync(TEntity entity)
    {
        _dbSet.Update(entity);
        return Task.CompletedTask;
        // Commit deve ser feito pelo UnitOfWork
    }

    public virtual Task DeleteAsync(TEntity entity, Guid idUsuario)
    {
        if(idUsuario == Guid.Empty)
            throw new Exception("Usuário inválido");
        entity.MarcarComoExcluido(idUsuario); // ou outro método da base
        _dbSet.Update(entity);
        return Task.CompletedTask;
        // Commit deve ser feito pelo UnitOfWork
    }
}