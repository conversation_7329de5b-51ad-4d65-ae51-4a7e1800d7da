<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.0" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Az.Upload.Domain\Az.Upload.Domain.csproj" />
    <ProjectReference Include="..\Az.Upload.Infrastructure\Az.Upload.Infrastructure.csproj" />
    <ProjectReference Include="..\Az.Upload.Application\Az.Upload.Application.csproj" />
    <ProjectReference Include="..\..\Az.Shared\Az.Shared.Shared\Az.Shared.Shared.csproj" />
  </ItemGroup>

</Project>
