using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Az.Upload.Application.Commands.UploadCommands.UploadImagens;
using Az.Upload.Domain.Enums;
using Az.Upload.Infrastructure.Services;
using Xunit;

namespace Az.Upload.Tests.Unit.Commands.UploadCommands.UploadImagens;

public class UploadImagensCommandHandlerTests
{
    private readonly Mock<IAzureBlobStorageService> _mockBlobStorageService;
    private readonly Mock<ILogger<UploadImagensCommandHandler>> _mockLogger;
    private readonly UploadImagensCommandHandler _handler;

    public UploadImagensCommandHandlerTests()
    {
        _mockBlobStorageService = new Mock<IAzureBlobStorageService>();
        _mockLogger = new Mock<ILogger<UploadImagensCommandHandler>>();
        _handler = new UploadImagensCommandHandler(_mockBlobStorageService.Object, _mockLogger.Object);
    }

    [Fact(DisplayName = "Deve fazer upload de imagens com sucesso")]
    public async Task Deve_Fazer_Upload_De_Imagens_Com_Sucesso()
    {
        // Arrange
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>
            {
                new("teste1.jpg", "image/jpeg", new byte[1024]),
                new("teste2.png", "image/png", new byte[2048])
            },
            TipoUploadEnum.Pacote
        );

        var urlsEsperadas = new List<string>
        {
            "https://storage.blob.core.windows.net/container/pacote/guid1.jpg",
            "https://storage.blob.core.windows.net/container/pacote/guid2.png"
        };

        _mockBlobStorageService
            .Setup(x => x.UploadImagensAsync(
                It.IsAny<List<(byte[], string, string)>>(),
                It.IsAny<TipoUploadEnum>(),
                It.IsAny<DateOnly?>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(urlsEsperadas);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeTrue();
        result.Data.Should().BeEquivalentTo(urlsEsperadas);
        
        _mockBlobStorageService.Verify(
            x => x.UploadImagensAsync(
                It.Is<List<(byte[], string, string)>>(list => list.Count == 2),
                TipoUploadEnum.Pacote,
                null,
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact(DisplayName = "Deve fazer upload de imagens para cardápio com data de referência")]
    public async Task Deve_Fazer_Upload_De_Imagens_Para_Cardapio_Com_Data_Referencia()
    {
        // Arrange
        var dataReferencia = DateOnly.FromDateTime(DateTime.Now);
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>
            {
                new("cardapio.jpg", "image/jpeg", new byte[1024])
            },
            TipoUploadEnum.Cardapio,
            dataReferencia
        );

        var urlsEsperadas = new List<string>
        {
            "https://storage.blob.core.windows.net/container/cardapio/segunda/guid.jpg"
        };

        _mockBlobStorageService
            .Setup(x => x.UploadImagensAsync(
                It.IsAny<List<(byte[], string, string)>>(),
                It.IsAny<TipoUploadEnum>(),
                It.IsAny<DateOnly?>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(urlsEsperadas);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeTrue();
        result.Data.Should().BeEquivalentTo(urlsEsperadas);
        
        _mockBlobStorageService.Verify(
            x => x.UploadImagensAsync(
                It.IsAny<List<(byte[], string, string)>>(),
                TipoUploadEnum.Cardapio,
                dataReferencia,
                It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
