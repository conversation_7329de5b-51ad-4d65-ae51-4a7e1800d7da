using FluentAssertions;
using Az.Upload.Application.Commands.UploadCommands.UploadImagens;
using Az.Upload.Domain.Enums;

namespace Az.Upload.Tests.Unit.Commands.UploadCommands.UploadImagens;

public class UploadImagensCommandValidatorTests
{
    private readonly UploadImagensCommandValidator _validator = new();

    [Fact(DisplayName = "Deve validar comando válido para cardápio")]
    public void Deve_Validar_Comando_Valido_Para_Cardapio()
    {
        // Arrange
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>
            {
                new("teste.jpg", "image/jpeg", new byte[1024])
            },
            TipoUploadEnum.Cardapio,
            DateOnly.FromDateTime(DateTime.Now)
        );

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact(DisplayName = "Deve validar comando válido para pacote")]
    public void Deve_Validar_Comando_Valido_Para_Pacote()
    {
        // Arrange
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>
            {
                new("teste.png", "image/png", new byte[1024])
            },
            TipoUploadEnum.Pacote
        );

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeTrue();
    }

    [Fact(DisplayName = "Deve falhar quando não há imagens")]
    public void Deve_Falhar_Quando_Nao_Ha_Imagens()
    {
        // Arrange
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>(),
            TipoUploadEnum.Pacote
        );

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage.Contains("Pelo menos uma imagem deve ser fornecida"));
    }

    [Fact(DisplayName = "Deve falhar quando cardápio não tem data de referência")]
    public void Deve_Falhar_Quando_Cardapio_Nao_Tem_Data_Referencia()
    {
        // Arrange
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>
            {
                new("teste.jpg", "image/jpeg", new byte[1024])
            },
            TipoUploadEnum.Cardapio
        );

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage.Contains("Data de referência é obrigatória para uploads do tipo Cardápio"));
    }

    [Fact(DisplayName = "Deve falhar quando tipo de arquivo é inválido")]
    public void Deve_Falhar_Quando_Tipo_Arquivo_Invalido()
    {
        // Arrange
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>
            {
                new("teste.pdf", "application/pdf", new byte[1024])
            },
            TipoUploadEnum.Pacote
        );

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage.Contains("Tipos de arquivo permitidos"));
    }

    [Fact(DisplayName = "Deve falhar quando arquivo é muito grande")]
    public void Deve_Falhar_Quando_Arquivo_Muito_Grande()
    {
        // Arrange
        var command = new UploadImagensCommand(
            new List<ImagemUploadDto>
            {
                new("teste.jpg", "image/jpeg", new byte[6 * 1024 * 1024]) // 6MB
            },
            TipoUploadEnum.Pacote
        );

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage.Contains("Tamanho máximo da imagem: 5MB"));
    }

    [Fact(DisplayName = "Deve falhar quando há mais de 10 imagens")]
    public void Deve_Falhar_Quando_Ha_Mais_De_10_Imagens()
    {
        // Arrange
        var imagens = Enumerable.Range(1, 11)
            .Select(i => new ImagemUploadDto($"teste{i}.jpg", "image/jpeg", new byte[1024]))
            .ToList();

        var command = new UploadImagensCommand(imagens, TipoUploadEnum.Pacote);

        // Act
        var result = _validator.Validate(command);

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.ErrorMessage.Contains("Máximo de 10 imagens por upload"));
    }
}
