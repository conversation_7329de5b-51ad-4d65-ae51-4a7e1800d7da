using FluentValidation;
using Az.Upload.Domain.Enums;

namespace Az.Upload.Application.Commands.UploadCommands.UploadImagens;

public class UploadImagensCommandValidator : AbstractValidator<UploadImagensCommand>
{
    private readonly string[] _tiposPermitidos = { "image/jpeg", "image/jpg", "image/png", "image/webp" };
    private const int _tamanhoMaximoMB = 5;
    private const int _tamanhoMaximoBytes = _tamanhoMaximoMB * 1024 * 1024;

    public UploadImagensCommandValidator()
    {
        RuleFor(x => x.Imagens)
            .NotEmpty()
            .WithMessage("Pelo menos uma imagem deve ser fornecida.")
            .Must(x => x.Count <= 10)
            .WithMessage("Máximo de 10 imagens por upload.");

        RuleFor(x => x.TipoUpload)
            .IsInEnum()
            .WithMessage("Tipo de upload inválido.");

        RuleFor(x => x.DataReferencia)
            .Must((command, dataReferencia) => ValidarDataReferencia(command.TipoUpload, dataReferencia))
            .WithMessage("Data de referência é obrigatória para uploads do tipo Cardápio.");

        RuleForEach(x => x.Imagens)
            .ChildRules(imagem =>
            {
                imagem.RuleFor(x => x.NomeArquivo)
                    .NotEmpty()
                    .WithMessage("Nome do arquivo é obrigatório.")
                    .Must(nome => nome.Length <= 255)
                    .WithMessage("Nome do arquivo deve ter no máximo 255 caracteres.");

                imagem.RuleFor(x => x.ContentType)
                    .NotEmpty()
                    .WithMessage("Tipo de conteúdo é obrigatório.")
                    .Must(tipo => _tiposPermitidos.Contains(tipo.ToLower()))
                    .WithMessage($"Tipos de arquivo permitidos: {string.Join(", ", _tiposPermitidos)}");

                imagem.RuleFor(x => x.Conteudo)
                    .NotEmpty()
                    .WithMessage("Conteúdo da imagem é obrigatório.")
                    .Must(conteudo => conteudo.Length <= _tamanhoMaximoBytes)
                    .WithMessage($"Tamanho máximo da imagem: {_tamanhoMaximoMB}MB");
            });
    }

    private static bool ValidarDataReferencia(TipoUploadEnum tipoUpload, DateOnly? dataReferencia)
    {
        return tipoUpload != TipoUploadEnum.Cardapio || dataReferencia.HasValue;
    }
}
