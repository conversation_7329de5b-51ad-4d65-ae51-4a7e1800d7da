using MediatR;
using Az.Upload.Domain.Enums;
using Az.Shared.Shared.Common;

namespace Az.Upload.Application.Commands.UploadCommands.UploadImagens;

public record UploadImagensCommand(
    List<ImagemUploadDto> Imagens,
    TipoUploadEnum TipoUpload,
    DateOnly? DataReferencia = null
) : IRequest<OperationResult<List<string>>>;

public record ImagemUploadDto(
    string NomeArquivo,
    string ContentType,
    byte[] Conteudo
);
