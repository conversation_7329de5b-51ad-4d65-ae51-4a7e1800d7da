using MediatR;
using Az.Upload.Infrastructure.Services;
using Az.Shared.Shared.Common;
using Microsoft.Extensions.Logging;

namespace Az.Upload.Application.Commands.UploadCommands.UploadImagens;

public class UploadImagensCommandHandler : IRequestHandler<UploadImagensCommand, OperationResult<List<string>>>
{
    private readonly IAzureBlobStorageService _azureBlobStorageService;
    private readonly ILogger<UploadImagensCommandHandler> _logger;

    public UploadImagensCommandHandler(
        IAzureBlobStorageService azureBlobStorageService,
        ILogger<UploadImagensCommandHandler> logger)
    {
        _azureBlobStorageService = azureBlobStorageService;
        _logger = logger;
    }

    public async Task<OperationResult<List<string>>> Handle(UploadImagensCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Iniciando upload de {Quantidade} imagens do tipo {TipoUpload}", 
            request.Imagens.Count, request.TipoUpload);

        var imagensParaUpload = request.Imagens
            .Select(img => (img.Conteudo, img.NomeArquivo, img.ContentType))
            .ToList();

        var urls = await _azureBlobStorageService.UploadImagensAsync(
            imagensParaUpload,
            request.TipoUpload,
            request.DataReferencia,
            cancellationToken);

        _logger.LogInformation("Upload concluído com sucesso. {Quantidade} URLs geradas", urls.Count);

        return OperationResult.Result(urls);
    }
}
