<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../Az.Upload.Domain/Az.Upload.Domain.csproj" />
    <ProjectReference Include="../Az.Upload.Infrastructure/Az.Upload.Infrastructure.csproj" />
    <ProjectReference Include="../../Az.Shared/Az.Shared.Shared/Az.Shared.Shared.csproj" />
  </ItemGroup>

</Project>
