using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Az.Upload.Domain.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace Az.Upload.Infrastructure.Services;

public class AzureBlobStorageService : IAzureBlobStorageService
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly ILogger<AzureBlobStorageService> _logger;
    private readonly string _containerName;
    private readonly string _baseUrl;

    public AzureBlobStorageService(
        IConfiguration configuration,
        ILogger<AzureBlobStorageService> logger)
    {
        var connectionString = configuration.GetConnectionString("AzureBlobStorage");
        _containerName = configuration["AzureBlobStorage:ContainerName"] ?? "azfood-images";
        _baseUrl = configuration["AzureBlobStorage:BaseUrl"] ?? "";
        
        _blobServiceClient = new BlobServiceClient(connectionString);
        _logger = logger;
    }

    public async Task<string> UploadImagemAsync(
        byte[] conteudo, 
        string nomeArquivo, 
        string contentType, 
        TipoUploadEnum tipoUpload, 
        DateOnly? dataReferencia = null,
        CancellationToken cancellationToken = default)
    {
        var caminhoCompleto = GerarCaminhoArquivo(nomeArquivo, tipoUpload, dataReferencia);
        
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        await containerClient.CreateIfNotExistsAsync(PublicAccessType.Blob, cancellationToken: cancellationToken);
        
        var blobClient = containerClient.GetBlobClient(caminhoCompleto);
        
        using var stream = new MemoryStream(conteudo);
        
        var blobHttpHeaders = new BlobHttpHeaders
        {
            ContentType = contentType
        };
        
        await blobClient.UploadAsync(
            stream, 
            new BlobUploadOptions 
            { 
                HttpHeaders = blobHttpHeaders,
                Conditions = null
            }, 
            cancellationToken);
        
        var url = string.IsNullOrEmpty(_baseUrl) 
            ? blobClient.Uri.ToString() 
            : $"{_baseUrl.TrimEnd('/')}/{caminhoCompleto}";
            
        _logger.LogInformation("Imagem {NomeArquivo} enviada com sucesso para {Url}", nomeArquivo, url);
        
        return url;
    }

    public async Task<List<string>> UploadImagensAsync(
        List<(byte[] conteudo, string nomeArquivo, string contentType)> imagens,
        TipoUploadEnum tipoUpload,
        DateOnly? dataReferencia = null,
        CancellationToken cancellationToken = default)
    {
        var urls = new List<string>();
        
        foreach (var (conteudo, nomeArquivo, contentType) in imagens)
        {
            var url = await UploadImagemAsync(conteudo, nomeArquivo, contentType, tipoUpload, dataReferencia, cancellationToken);
            urls.Add(url);
        }
        
        return urls;
    }

    public async Task<bool> DeletarImagemAsync(string url, CancellationToken cancellationToken = default)
    {
        var nomeBlob = ExtrairNomeBlobDaUrl(url);
        if (string.IsNullOrEmpty(nomeBlob))
            return false;
            
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        var blobClient = containerClient.GetBlobClient(nomeBlob);
        
        var response = await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
        
        _logger.LogInformation("Tentativa de exclusão da imagem {Url}. Sucesso: {Sucesso}", url, response.Value);
        
        return response.Value;
    }

    private static string GerarCaminhoArquivo(string nomeArquivo, TipoUploadEnum tipoUpload, DateOnly? dataReferencia)
    {
        var extensao = Path.GetExtension(nomeArquivo);
        var guid = Guid.NewGuid();
        var nomeArquivoFinal = $"{guid}{extensao}";
        
        return tipoUpload switch
        {
            TipoUploadEnum.Cardapio => GerarCaminhoCardapio(nomeArquivoFinal, dataReferencia!.Value),
            TipoUploadEnum.Pacote => $"pacote/{nomeArquivoFinal}",
            _ => throw new ArgumentException($"Tipo de upload não suportado: {tipoUpload}")
        };
    }

    private static string GerarCaminhoCardapio(string nomeArquivo, DateOnly dataReferencia)
    {
        var diaSemana = dataReferencia.DayOfWeek switch
        {
            DayOfWeek.Sunday => "domingo",
            DayOfWeek.Monday => "segunda",
            DayOfWeek.Tuesday => "terca",
            DayOfWeek.Wednesday => "quarta",
            DayOfWeek.Thursday => "quinta",
            DayOfWeek.Friday => "sexta",
            DayOfWeek.Saturday => "sabado",
            _ => "indefinido"
        };
        
        return $"cardapio/{diaSemana}/{nomeArquivo}";
    }

    private string ExtrairNomeBlobDaUrl(string url)
    {
        if (string.IsNullOrEmpty(url))
            return string.Empty;
            
        var uri = new Uri(url);
        var segments = uri.Segments;
        
        // Remove o primeiro segmento que é "/"
        var caminhoCompleto = string.Join("", segments.Skip(2));
        
        return caminhoCompleto;
    }
}
