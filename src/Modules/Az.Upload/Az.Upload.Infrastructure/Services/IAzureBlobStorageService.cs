using Az.Upload.Domain.Enums;

namespace Az.Upload.Infrastructure.Services;

public interface IAzureBlobStorageService
{
    Task<string> UploadImagemAsync(
        byte[] conteudo, 
        string nomeArquivo, 
        string contentType, 
        TipoUploadEnum tipoUpload, 
        DateOnly? dataReferencia = null,
        CancellationToken cancellationToken = default);

    Task<List<string>> UploadImagensAsync(
        List<(byte[] conteudo, string nomeArquivo, string contentType)> imagens,
        TipoUploadEnum tipoUpload,
        DateOnly? dataReferencia = null,
        CancellationToken cancellationToken = default);

    Task<bool> DeletarImagemAsync(string url, CancellationToken cancellationToken = default);
}
