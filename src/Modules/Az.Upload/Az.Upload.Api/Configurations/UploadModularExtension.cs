using System.Reflection;
using Az.Upload.Application.Commands.UploadCommands.UploadImagens;
using Az.Upload.Infrastructure.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Az.Upload.Api.Configurations;

public static class UploadModularExtension
{
    public static void AddModule(IServiceCollection services, IConfiguration configuration, List<Assembly> assemblies)
    {
        // Register all assemblies
        assemblies.Add(typeof(UploadModularExtension).Assembly);
        assemblies.Add(typeof(UploadImagensCommand).Assembly);

        // Register Azure Blob Storage Service
        services.AddScoped<IAzureBlobStorageService, AzureBlobStorageService>();

        // Register application services
        services.Scan(scan => scan
            .FromAssemblyOf<UploadImagensCommand>()
            .AddClasses(classes => classes.Where(type => type.Name.EndsWith("Service") && !type.IsInterface))
            .AsImplementedInterfaces()
            .WithScopedLifetime());
    }
}
