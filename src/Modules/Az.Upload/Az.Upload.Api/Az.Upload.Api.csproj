<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="Scrutor" Version="6.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../Az.Upload.Application/Az.Upload.Application.csproj" />
    <ProjectReference Include="../Az.Upload.Infrastructure/Az.Upload.Infrastructure.csproj" />
    <ProjectReference Include="../../Az.Shared/Az.Shared.Shared/Az.Shared.Shared.csproj" />
  </ItemGroup>

</Project>
