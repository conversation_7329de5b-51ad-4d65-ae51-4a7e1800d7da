using MediatR;
using Az.Upload.Application.Commands.UploadCommands.UploadImagens;
using Az.Upload.Domain.Enums;
using Az.Shared.Shared.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace Az.Upload.Api.MapEndpoints;

public static class MapUploadEndpoints
{
    public static void MapUpload(this WebApplication app)
    {
        app.MapPost("/v1/upload/imagens", [Authorize] async (
            IFormFileCollection files,
            int tipoUpload,
            DateOnly? dataReferencia,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            if (!files.Any())
                return Results.BadRequest(OperationResult.Error<List<string>>(["Nenhuma imagem foi enviada."]));

            if (!Enum.IsDefined(typeof(TipoUploadEnum), tipoUpload))
                return Results.BadRequest(OperationResult.Error<List<string>>(["Tipo de upload inválido."]));

            var tipoUploadEnum = (TipoUploadEnum)tipoUpload;

            var imagens = new List<ImagemUploadDto>();
            
            foreach (var file in files)
            {
                using var memoryStream = new MemoryStream();
                await file.CopyToAsync(memoryStream, cancellationToken);
                
                imagens.Add(new ImagemUploadDto(
                    file.FileName,
                    file.ContentType,
                    memoryStream.ToArray()
                ));
            }

            var command = new UploadImagensCommand(imagens, tipoUploadEnum, dataReferencia);
            var result = await sender.Send(command, cancellationToken);

            return result.Success 
                ? Results.Ok(result) 
                : Results.BadRequest(result);
        })
        .WithName("UploadImagens")
        .WithTags("Upload")
        .RequireAuthorization()
        .Accepts<IFormFileCollection>("multipart/form-data")
        .Produces<OperationResult<List<string>>>(StatusCodes.Status200OK)
        .Produces<OperationResult<List<string>>>(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status401Unauthorized);
    }
}
