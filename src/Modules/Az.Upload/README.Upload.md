# Mó<PERSON><PERSON> Upload

Este módulo é responsável pelo gerenciamento de upload de imagens para o Azure Blob Storage, organizando os arquivos por tipo e data de referência.

---

## Estrutura do Projeto

| Projeto | Descrição |
|---------|-----------|
| **Az.Upload.Domain** | Enums e regras de negócio |
| **Az.Upload.Application** | Comandos, queries, DTOs e validações |
| **Az.Upload.Infrastructure** | Implementação do Azure Blob Storage |
| **Az.Upload.Api** | Endpoints e configurações específicas do módulo |
| **Az.Upload.Tests** | Testes unitários e de integração |

---

## Regras de Negócio

### Upload de Imagens
- O sistema deve permitir upload de uma ou mais imagens
- Tipos de arquivo permitidos: JPEG, JPG, PNG, WEBP
- Tamanho máximo por imagem: 5MB
- Máximo de 10 imagens por upload
- As imagens são armazenadas no Azure Blob Storage
- Não há necessidade de armazenar metadados no banco de dados

### Organização por Tipo
- **Cardápio**: Armazenado em `cardapio/{diaDaSemana}/{Guid}.extensao`
- **Pacote**: Armazenado em `pacote/{Guid}.extensao`
- Para uploads do tipo Cardápio, a data de referência é obrigatória

### Estrutura de Pastas
- **Cardápio**: `cardapio/segunda/`, `cardapio/terca/`, etc.
- **Pacote**: `pacote/`

---

## Endpoints

### Upload

#### POST /v1/upload/imagens
- **Finalidade:** Faz upload de uma ou mais imagens
- **Autorização:** Requerida
- **Content-Type:** multipart/form-data
- **Parâmetros:**
  - `files`: Arquivos de imagem (IFormFileCollection)
  - `tipoUpload`: Tipo do upload (1=Cardapio, 2=Pacote)
  - `dataReferencia`: Data de referência (obrigatória para Cardápio)
- **Retorno:** OperationResult<List<string>> (URLs das imagens)

---

## Estrutura da Application

```
src/Modules/Az.Upload/Az.Upload.Application/
├── Commands/
│   └── UploadCommands/
│       └── UploadImagens/
│           ├── UploadImagensCommand.cs
│           ├── UploadImagensCommandHandler.cs
│           └── UploadImagensCommandValidator.cs
```

### Regras de Organização

- Todos os comandos ficam em `Commands/UploadCommands/`
- Cada operação tem sua própria subpasta
- DTOs são definidos junto com os comandos

---

## Estrutura da Domain

```
Domain/
├── Enums/
│   └── TipoUploadEnum.cs
```

### Regras de Implementação

- Enum define os tipos de upload disponíveis
- Não há entidades pois não armazenamos no banco de dados

---

## Estrutura da Infrastructure

```
Infrastructure/
└── Services/
    ├── IAzureBlobStorageService.cs
    └── AzureBlobStorageService.cs
```

### Regras de Implementação

- Serviço responsável pela comunicação com Azure Blob Storage
- Geração automática de nomes únicos para arquivos
- Organização automática por tipo e data

---

## Estrutura da Testes

```
Tests/
└── Unit/
    └── Commands/   
        └── UploadCommands/
            └── UploadImagens/
                ├── UploadImagensCommandHandlerTests.cs
                └── UploadImagensCommandValidatorTests.cs
```

---

## Commands (Operações de Escrita)

### UploadImagens

#### UploadImagensCommand
- **Finalidade:** Faz upload de múltiplas imagens para o Azure Blob Storage
- **Localização:** `Commands/UploadCommands/UploadImagens/`
- **Retorno:** `OperationResult<List<string>>`
- **Endpoint:** `POST /v1/upload/imagens`
- **Validações:** 
  - Pelo menos uma imagem obrigatória
  - Máximo 10 imagens por upload
  - Tipos de arquivo permitidos: image/jpeg, image/jpg, image/png, image/webp
  - Tamanho máximo: 5MB por imagem
  - Data de referência obrigatória para tipo Cardápio
- **Regras:** 
  - Gera GUID único para cada arquivo
  - Organiza por tipo e data conforme especificado
  - Retorna lista de URLs das imagens enviadas

---

## Observações Importantes

| Item | Descrição |
|------|-----------|
| **Armazenamento** | Azure Blob Storage apenas, sem banco de dados |
| **Organização** | Por tipo e data de referência |
| **Segurança** | Autorização obrigatória para upload |
| **Performance** | Upload assíncrono de múltiplas imagens |

---

## Enums

### TipoUploadEnum
- **Cardapio** = 1
- **Pacote** = 2

---

## Configuração

### Azure Blob Storage

Adicione as seguintes configurações no `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "AzureBlobStorage": "DefaultEndpointsProtocol=https;AccountName=...;AccountKey=...;EndpointSuffix=core.windows.net"
  },
  "AzureBlobStorage": {
    "ContainerName": "azfood-images",
    "BaseUrl": "https://yourstorageaccount.blob.core.windows.net/azfood-images"
  }
}
```

### Adicionando as Configurações no Program.cs

```csharp
// Add Modules
UploadModularExtension.AddModule(builder.Services, builder.Configuration, mediatoRAssemblies);
```

---

## Regras de Implementação

- Não usar try-catch blocks no código da aplicação
- Usar validações com FluentValidation
- Logs estruturados para monitoramento
- Geração automática de nomes únicos para evitar conflitos
- Organização automática por tipo e data de referência
