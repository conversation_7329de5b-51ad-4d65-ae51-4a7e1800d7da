using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Az.Shared.Shared.Entities;

public abstract class BaseEntidadeMap<T> : IEntityTypeConfiguration<T> where T : BaseEntidade
{
    public virtual void Configure(EntityTypeBuilder<T> builder)
    {
        builder.Property(e => e.Ativo).IsRequired();
        builder.Property(e => e.Excluido).IsRequired();
        builder.Property(e => e.DataDeCriacao).IsRequired();
        builder.Property(e => e.DataDeAlteracao);
        builder.Property(e => e.UsuarioIdAlteracao);
    }
}