using System;

namespace Az.Shared.Shared.Entities;

/// <summary>
/// Classe base para todas as entidades do domínio.
/// Cada entidade deve ter uma propriedade Id do tipo Guid, nomeada como Id + NomeDaEntidade (ex: IdFeriado, IdHorarioFuncionamento).
/// </summary>
public abstract class BaseEntidade
{
    // Cada entidade concreta deve declarar sua própria propriedade Id
    public bool Ativo { get; private set; }
    public bool Excluido { get; private set; }
    public DateTime DataDeCriacao { get; private set; }
    public DateTime? DataDeAlteracao { get; private set; }
    public Guid? UsuarioIdAlteracao { get; private set; }

    protected BaseEntidade()
    {
        Ativo = true;
        Excluido = false;
        DataDeCriacao = DateTime.UtcNow;
    }

    public void MarcarComoExcluido(Guid usuarioId)
    {
        Excluido = true;
        Ativo = false;
        DataDeAlteracao = DateTime.UtcNow;
        UsuarioIdAlteracao = usuarioId;
    }

    public void MarcarComoAtivo(Guid usuarioId)
    {
        Ativo = true;
        Excluido = false;
        DataDeAlteracao = DateTime.UtcNow;
        UsuarioIdAlteracao = usuarioId;
    }

    public void MarcarComoInativo(Guid usuarioId)
    {
        Ativo = false;
        DataDeAlteracao = DateTime.UtcNow;
        UsuarioIdAlteracao = usuarioId;
    }

    public void AtualizarAlteracao(Guid usuarioId)
    {
        DataDeAlteracao = DateTime.UtcNow;
        UsuarioIdAlteracao = usuarioId;
    }
    
    
}