using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace Az.Shared.Shared.Extensions;

public static class MigrationExtensions
{
    public static void ApplyMigrations<TContext>(this WebApplication app) where TContext : DbContext
    {
        Console.WriteLine($"Aplicando migrations para {typeof(TContext).Name}...");
        using var scope = app.Services.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<TContext>();
        if(context.Database.GetPendingMigrations().Any())
        {
            Console.WriteLine("Possuí migrations!");
            context.Database.Migrate();
        }
        else Console.WriteLine("Não possuí migrations!");
        
    }
}
