using System.Collections.Generic;
using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using Az.Shared.Shared.Services.Interfaces;

namespace Az.Shared.Shared.Services;

public class AspNetCurrentUser : ICurrentUser
{
    private readonly IHttpContextAccessor _accessor;

    public AspNetCurrentUser(IHttpContextAccessor accessor)
    {
        _accessor = accessor;
    }

    public string Name => _accessor.HttpContext?.User?.Identity?.Name ?? string.Empty;

    public bool IsAuthenticated => _accessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

    public string UserId => _accessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;

    public string Email => _accessor.HttpContext?.User?.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty;

    public IEnumerable<Claim> GetClaims()
    {
        return _accessor.HttpContext?.User?.Claims ?? new List<Claim>();
    }
}