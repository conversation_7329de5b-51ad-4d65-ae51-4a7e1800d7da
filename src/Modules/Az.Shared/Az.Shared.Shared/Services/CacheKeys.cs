namespace Az.Shared.Shared.Services;

/// <summary>
/// Constantes para chaves de cache compartilhadas entre módulos
/// Estrutura hierárquica: {aplicacao}:{modulo}:{entidade}:{operacao}:{parametros}
/// </summary>
public static class CacheKeys
{
    /// <summary>
    /// Prefixo base para toda a aplicação
    /// </summary>
    private const string ApplicationPrefix = "azfood";

    /// <summary>
    /// Chaves do módulo Administrativo
    /// </summary>
    public static class Administrativo
    {
        private const string ModulePrefix = $"{ApplicationPrefix}:administrativo";
        /// <summary>
        /// Chaves relacionadas a datas disponíveis
        /// </summary>
        public static class DatasDisponiveis
        {
            public const string ParaInicio = $"{ModulePrefix}:datas-disponiveis:para-inicio";
            public const string Pattern = $"{ModulePrefix}:datas-disponiveis:*";
            
            /// <summary>
            /// Gera chave para datas disponíveis com base na data de referência
            /// </summary>
            /// <param name="dataReferencia">Data de referência (hoje)</param>
            /// <returns>Chave do cache</returns>
            public static string ParaInicioComData(DateOnly dataReferencia) => 
                $"{ModulePrefix}:datas-disponiveis:para-inicio:{dataReferencia:yyyy-MM-dd}";
        }

        /// <summary>
        /// Chaves relacionadas a feriados
        /// </summary>
        public static class Feriados
        {
            public const string All = $"{ModulePrefix}:feriados:all";
            public const string Pattern = $"{ModulePrefix}:feriados:*";
            public static string ById(Guid id) => $"{ModulePrefix}:feriados:id:{id}";
            public static string ByDate(DateOnly data) => $"{ModulePrefix}:feriados:date:{data:yyyy-MM-dd}";
            public static string ByDateRange(DateOnly inicio, DateOnly fim) => 
                $"{ModulePrefix}:feriados:range:{inicio:yyyy-MM-dd}:{fim:yyyy-MM-dd}";
        }

        /// <summary>
        /// Chaves relacionadas a horários de funcionamento
        /// </summary>
        public static class HorariosFuncionamento
        {
            public const string All = $"{ModulePrefix}:horarios:all";
            public const string Pattern = $"{ModulePrefix}:horarios:*";
            public static string ById(Guid id) => $"{ModulePrefix}:horarios:id:{id}";
            public static string ByDiaSemana(DayOfWeek diaSemana) => $"{ModulePrefix}:horarios:dia:{(int)diaSemana}";
        }
    }

    /// <summary>
    /// Configurações de expiração padrão
    /// </summary>
    public static class Expiration
    {
        /// <summary>
        /// Expiração padrão de 1 dia
        /// </summary>
        public static readonly TimeSpan OneDay = TimeSpan.FromDays(1);
    }
}
