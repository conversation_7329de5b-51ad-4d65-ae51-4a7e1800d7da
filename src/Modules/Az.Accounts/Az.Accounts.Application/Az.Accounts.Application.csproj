<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />    
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.12.0" />        
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Az.Shared\Az.Shared.Shared\Az.Shared.Shared.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
