using MediatR;
using Az.Accounts.Application.Commands.CreateUser;
using Az.Accounts.Application.Commands.Login;
using Az.Accounts.Application.Commands.ChangePassword;
using Az.Shared.Shared.Common;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace Az.Accounts.Api.MapEndpoints;

public static class MapAccountEndpoints
{
    public static void MapAccounts(this WebApplication app)
    {
        app.MapPost("/v1/accounts/create-user", async (CreateUserCommand command, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(command, cancellationToken);
            return result.Success ?
                Results.Created($"/v1/accounts/{result.Data}", result) :
                Results.BadRequest(result.Messages);
        })
        .AllowAnonymous()
        .WithName("CreateUser")
        .WithTags("Accounts")
        .Produces<OperationResult<string>>(StatusCodes.Status201Created)
        .Produces<OperationResult<string>>(StatusCodes.Status400BadRequest);

        app.MapPost("/v1/accounts/login", async (LoginCommand command, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(command, cancellationToken);
            return result.Success ?
                Results.Ok(result) :
                Results.BadRequest(result.Messages);
        })
        .AllowAnonymous()
        .WithName("Login")
        .WithTags("Accounts")
        .Produces<OperationResult<string>>(StatusCodes.Status200OK)
        .Produces<OperationResult<string>>(StatusCodes.Status400BadRequest);

        app.MapPost("/v1/accounts/change-password", async (ChangePasswordCommand command, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(command);
            return result.Success ?
                Results.Ok(result) :
                Results.BadRequest(result.Messages);
        })
        .WithName("ChangePassword")
        .WithTags("Accounts")
        .RequireAuthorization()
        .Produces<OperationResult<bool>>(StatusCodes.Status200OK)
        .Produces<OperationResult<bool>>(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status401Unauthorized);

        // Endpoint protegido para teste de autenticação
        app.MapGet("/v1/accounts/protected-test", [Authorize] (HttpContext context) =>
        {
            var userId = context.User.FindFirstValue(ClaimTypes.NameIdentifier) ?? context.User.FindFirstValue("sub");
            var userName = context.User.FindFirstValue(ClaimTypes.Name);
            var userEmail = context.User.FindFirstValue(ClaimTypes.Email);

            return Results.Ok(new { 
                message = "Você está autenticado!", 
                userId, 
                userName, 
                userEmail,
                headers = context.Request.Headers.Where(h => h.Key == "Authorization")
                    .ToDictionary(h => h.Key, h => h.Value.ToString())
            });
        })
        .WithName("ProtectedTest")
        .WithTags("Accounts")
        .Produces<object>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status401Unauthorized);
    }
}