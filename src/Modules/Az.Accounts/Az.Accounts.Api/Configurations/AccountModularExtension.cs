using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.Reflection;
using Az.Accounts.Infrastructure.Context;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Az.Accounts.Application.Commands.CreateUser;

namespace Az.Accounts.Api.Configurations;

public static class AccountModularExtension
{
    public static void AddModule(IServiceCollection services, IConfiguration configuration, List<Assembly> assemblies)
    {
        //Configução do Banco
        services.AddDbContext<AccountsDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("DefaultConnection"),
                b => b.MigrationsHistoryTable("__EFMigrationsHistory", "Accounts")));

        //Autorização
        services.AddIdentityCore<IdentityUser>()
            .AddRoles<IdentityRole>()
            .AddErrorDescriber<CustomIdentityErrorDescriber>()
            .AddEntityFrameworkStores<AccountsDbContext>()
            .AddSignInManager();

        assemblies.Add(typeof(AccountModularExtension).Assembly);
        assemblies.Add(typeof(CreateUserCommand).Assembly);
    }
}