using System.Text;
using Az.Accounts.Application.Commands.ChangePassword;
using Az.Accounts.Application.Commands.CreateUser;
using Az.Accounts.Application.Commands.Login;
using Az.Accounts.Application.Enums;
using Az.Accounts.Tests.Integration._Commom;
using Az.Shared.Shared.Common;
using Newtonsoft.Json;

namespace Az.Accounts.Tests.Integration;

public class CrudAccount : IClassFixture<AccountsControlAppFactory>
{
    private readonly AccountsControlAppFactory _factory;
    private readonly HttpClient _client;


    public CrudAccount(AccountsControlAppFactory factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task CreateAccount()
    {
        var command = new CreateUserCommand(
           Email: "<EMAIL>",
           Password: "Test@123",
           ConfirmaPassword: "Test@123",
           Perfil: PerfilEnum.Administrador
       );

        var response = await _client.PostAsync("/v1/accounts/create-user",
            new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

        response.EnsureSuccessStatusCode();

        var responseBody = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<OperationResult<string>>(responseBody);

        Assert.True(result!.Success);
        Assert.NotNull(result.Data);
    }

    [Fact]
    public async Task Login()
    {
        var command = new LoginCommand("<EMAIL>", "Test@123");
        var response = await _client.PostAsync("/v1/accounts/login",
            new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

        response.EnsureSuccessStatusCode();

        var responseBody = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<OperationResult<string>>(responseBody);

        Assert.True(result!.Success);
        Assert.NotNull(result.Data);
    }

    [Fact]
    public async Task ChangePassword()
    {
        // Primeiro, fazer login para obter o token
        var loginCommand = new LoginCommand("<EMAIL>", "Test@123");
        var loginResponse = await _client.PostAsync("/v1/accounts/login",
            new StringContent(JsonConvert.SerializeObject(loginCommand), Encoding.UTF8, "application/json"));

        loginResponse.EnsureSuccessStatusCode();

        var loginResponseBody = await loginResponse.Content.ReadAsStringAsync();
        var loginResult = JsonConvert.DeserializeObject<OperationResult<string>>(loginResponseBody);

        Assert.True(loginResult!.Success);
        Assert.NotNull(loginResult.Data);

        // Usar o token para a operação de alteração de senha
        var token = loginResult.Data;

        var command = new ChangePasswordCommand(
            Email: "<EMAIL>",
            CurrentPassword: "Test@123",
            NewPassword: "Test@1234"
        );

        _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var response = await _client.PostAsync("/v1/accounts/change-password",
            new StringContent(JsonConvert.SerializeObject(command), Encoding.UTF8, "application/json"));

        response.EnsureSuccessStatusCode();

        var responseBody = await response.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<OperationResult<bool>>(responseBody);

        Assert.True(result!.Success);
        Assert.True(result.Data);
    }
}