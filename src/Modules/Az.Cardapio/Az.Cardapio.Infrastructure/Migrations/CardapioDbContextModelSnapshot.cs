// <auto-generated />
using System;
using Az.Cardapio.Infrastructure.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Az.Cardapio.Infrastructure.Migrations
{
    [DbContext(typeof(CardapioDbContext))]
    partial class CardapioDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("Cardapio")
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Az.Cardapio.Domain.Entities.Acompanhamento", b =>
                {
                    b.Property<Guid>("IdAcompanhamento")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Ativo")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("DataDeAlteracao")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DataDeCriacao")
                        .HasColumnType("datetime2");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("Excluido")
                        .HasColumnType("bit");

                    b.Property<Guid>("IdCardapio")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ImagemUrl")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Ordem")
                        .HasColumnType("int");

                    b.Property<int>("Tipo")
                        .HasColumnType("int");

                    b.Property<Guid?>("UsuarioIdAlteracao")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("IdAcompanhamento");

                    b.HasIndex("IdCardapio")
                        .HasDatabaseName("IX_Acompanhamento_IdCardapio");

                    b.HasIndex("IdCardapio", "Ordem")
                        .IsUnique()
                        .HasDatabaseName("IX_Acompanhamento_IdCardapio_Ordem");

                    b.ToTable("Acompanhamento", "Cardapio");
                });

            modelBuilder.Entity("Az.Cardapio.Domain.Entities.Cardapio", b =>
                {
                    b.Property<Guid>("IdCardapio")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Ativo")
                        .HasColumnType("bit");

                    b.Property<DateOnly>("DataCardapio")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DataDeAlteracao")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DataDeCriacao")
                        .HasColumnType("datetime2");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("DescricaoProteina")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("Excluido")
                        .HasColumnType("bit");

                    b.Property<Guid>("IdProteina")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ImagensUrls")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Lowcarb")
                        .HasColumnType("bit");

                    b.Property<int>("Ordem")
                        .HasColumnType("int");

                    b.Property<string>("Titulo")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<Guid?>("UsuarioIdAlteracao")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("IdCardapio");

                    b.HasIndex("DataCardapio")
                        .HasDatabaseName("IX_Cardapio_DataCardapio");

                    b.HasIndex("DataCardapio", "Ordem")
                        .IsUnique()
                        .HasDatabaseName("IX_Cardapio_DataCardapio_Ordem");

                    b.HasIndex("DataCardapio", "Titulo")
                        .IsUnique()
                        .HasDatabaseName("IX_Cardapio_DataCardapio_Titulo");

                    b.ToTable("Cardapio", "Cardapio");
                });

            modelBuilder.Entity("Az.Cardapio.Domain.Entities.Acompanhamento", b =>
                {
                    b.HasOne("Az.Cardapio.Domain.Entities.Cardapio", "Cardapio")
                        .WithMany("Acompanhamentos")
                        .HasForeignKey("IdCardapio")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Cardapio");
                });

            modelBuilder.Entity("Az.Cardapio.Domain.Entities.Cardapio", b =>
                {
                    b.Navigation("Acompanhamentos");
                });
#pragma warning restore 612, 618
        }
    }
}
