using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Az.Shared.Shared.Entities;
using System.Text.Json;

namespace Az.Cardapio.Infrastructure.Mappings;

public class CardapioMap : BaseEntidadeMap<Domain.Entities.Cardapio>
{
    public override void Configure(EntityTypeBuilder<Domain.Entities.Cardapio> builder)
    {
        base.Configure(builder); // Mapeia campos herdados

        builder.ToTable("Cardapio");
        builder.HasKey(c => c.IdCardapio);

        builder.Property(c => c.IdCardapio)
            .IsRequired();

        builder.Property(c => c.DataCardapio)
            .IsRequired();

        builder.Property(c => c.Titulo)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(c => c.Descricao)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(c => c.Ordem)
            .IsRequired();

        builder.Property(c => c.IdProteina)
            .IsRequired();

        builder.Property(c => c.DescricaoProteina)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(c => c.ImagensUrls)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>())
            .HasColumnType("nvarchar(max)");

        builder.Property(c => c.Lowcarb)
            .IsRequired();

        // Relacionamento com Acompanhamentos
        builder.HasMany(c => c.Acompanhamentos)
            .WithOne(a => a.Cardapio)
            .HasForeignKey(a => a.IdCardapio)
            .OnDelete(DeleteBehavior.Cascade);

        // Índices para melhorar performance
        builder.HasIndex(c => c.DataCardapio)
            .HasDatabaseName("IX_Cardapio_DataCardapio");

        builder.HasIndex(c => new { c.DataCardapio, c.Titulo })
            .IsUnique()
            .HasDatabaseName("IX_Cardapio_DataCardapio_Titulo");

        builder.HasIndex(c => new { c.DataCardapio, c.Ordem })
            .IsUnique()
            .HasDatabaseName("IX_Cardapio_DataCardapio_Ordem");
    }
}
