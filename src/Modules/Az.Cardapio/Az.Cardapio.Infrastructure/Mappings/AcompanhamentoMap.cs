using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Az.Cardapio.Domain.Entities;
using Az.Shared.Shared.Entities;

namespace Az.Cardapio.Infrastructure.Mappings;

public class AcompanhamentoMap : BaseEntidadeMap<Acompanhamento>
{
    public override void Configure(EntityTypeBuilder<Acompanhamento> builder)
    {
        base.Configure(builder); // Mapeia campos herdados

        builder.ToTable("Acompanhamento");
        builder.HasKey(a => a.IdAcompanhamento);

        builder.Property(a => a.IdAcompanhamento)
            .IsRequired();

        builder.Property(a => a.IdCardapio)
            .IsRequired();

        builder.Property(a => a.Nome)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(a => a.Descricao)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(a => a.Tipo)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(a => a.Ordem)
            .IsRequired();

        builder.Property(a => a.ImagemUrl)
            .HasMaxLength(500);

        // Relacionamento com Cardapio
        builder.HasOne(a => a.Cardapio)
            .WithMany(c => c.Acompanhamentos)
            .HasForeignKey(a => a.IdCardapio)
            .OnDelete(DeleteBehavior.Cascade);

        // Índices para melhorar performance
        builder.HasIndex(a => a.IdCardapio)
            .HasDatabaseName("IX_Acompanhamento_IdCardapio");

        builder.HasIndex(a => new { a.IdCardapio, a.Ordem })
            .IsUnique()
            .HasDatabaseName("IX_Acompanhamento_IdCardapio_Ordem");
    }
}
