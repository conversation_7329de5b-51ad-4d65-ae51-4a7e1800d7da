using Az.Cardapio.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace Az.Cardapio.Infrastructure.Context;

public class CardapioDbContext : DbContext
{
    public CardapioDbContext(DbContextOptions<CardapioDbContext> options) : base(options)
    {
    }

    public DbSet<Domain.Entities.Cardapio> Cardapios { get; set; }
    public DbSet<Acompanhamento> Acompanhamentos { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.HasDefaultSchema("Cardapio");
        builder.ApplyConfigurationsFromAssembly(typeof(CardapioDbContext).Assembly);
    }
}
