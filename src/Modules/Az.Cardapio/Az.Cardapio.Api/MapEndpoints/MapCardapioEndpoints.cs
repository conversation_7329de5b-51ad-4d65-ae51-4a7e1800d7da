using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using MediatR;
using Az.Shared.Shared.Common;
using Az.Cardapio.Application.Commands.CardapioCommands.CreateCardapio;
using Az.Cardapio.Application.Commands.CardapioCommands.UpdateCardapio;
using Az.Cardapio.Application.Commands.CardapioCommands.DeleteCardapio;
using Az.Cardapio.Application.Queries.CardapioQueries.GetCardapioById;
using Az.Cardapio.Application.Queries.CardapioQueries.GetCardapiosResumidosByData;
using Az.Cardapio.Application.Queries.CardapioQueries.GetCardapioComAcompanhamentosByData;

using CardapioByIdResponse = Az.Cardapio.Application.Queries.CardapioQueries.GetCardapioById.GetCardapioByIdResponse;
using CardapiosResumidosResponse = Az.Cardapio.Application.Queries.CardapioQueries.GetCardapiosResumidosByData.GetCardapiosResumidosByDataResponse;
using CardapioComAcompanhamentosResponse = Az.Cardapio.Application.Queries.CardapioQueries.GetCardapioComAcompanhamentosByData.GetCardapioComAcompanhamentosByDataResponse;

namespace Az.Cardapio.Api.MapEndpoints;

public static class MapCardapioEndpoints
{
    public static void MapCardapio(this WebApplication app)
    {
        app.MapPost("/v1/cardapios", async (CreateCardapioCommand command, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(command, cancellationToken);
            return result.Success ?
                Results.Created($"/v1/cardapios/{result.Data}", result) :
                Results.BadRequest(result.Messages);
        })
        .WithName("CreateCardapio")
        .WithTags("Cardápios")
        .RequireAuthorization()
        .Produces<OperationResult<Guid>>(StatusCodes.Status201Created)
        .Produces<OperationResult<Guid>>(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status401Unauthorized);

        app.MapPut("/v1/cardapios/{id}", async (Guid id, UpdateCardapioCommand command, ISender sender, CancellationToken cancellationToken) =>
        {
            if (id != command.IdCardapio)
                return Results.BadRequest("Id do path e do body não conferem");
            var result = await sender.Send(command, cancellationToken);
            return result.Success ? Results.Ok(result) : Results.BadRequest(result.Messages);
        })
        .WithName("UpdateCardapio")
        .WithTags("Cardápios")
        .RequireAuthorization()
        .Produces<OperationResult<bool>>(StatusCodes.Status200OK)
        .Produces<OperationResult<bool>>(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status401Unauthorized);

        app.MapDelete("/v1/cardapios/{id}", async (Guid id, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new DeleteCardapioCommand(id), cancellationToken);
            return result.Success ? Results.Ok(result) : Results.BadRequest(result.Messages);
        })
        .WithName("DeleteCardapio")
        .WithTags("Cardápios")
        .RequireAuthorization()
        .Produces<OperationResult<bool>>(StatusCodes.Status200OK)
        .Produces<OperationResult<bool>>(StatusCodes.Status400BadRequest)
        .Produces(StatusCodes.Status401Unauthorized);

        app.MapGet("/v1/cardapios/{id}", async (Guid id, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new GetCardapioByIdQuery(id), cancellationToken);
            return result.Success ? Results.Ok(result) : Results.NotFound(result.Messages);
        })
        .WithName("GetCardapioById")
        .WithTags("Cardápios")
        .RequireAuthorization()
        .Produces<OperationResult<CardapioByIdResponse>>(StatusCodes.Status200OK)
        .Produces<OperationResult<CardapioByIdResponse>>(StatusCodes.Status404NotFound)
        .Produces(StatusCodes.Status401Unauthorized);

        app.MapGet("/v1/cardapios/resumidos", async (DateOnly data, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new GetCardapiosResumidosByDataQuery(data), cancellationToken);
            return Results.Ok(result);
        })
        .WithName("GetCardapiosResumidosByData")
        .WithTags("Cardápios")
        .RequireAuthorization()
        .Produces<OperationResult<List<CardapiosResumidosResponse>>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status401Unauthorized);

        app.MapGet("/v1/cardapios/completos", async (DateOnly data, ISender sender, CancellationToken cancellationToken) =>
        {
            var result = await sender.Send(new GetCardapioComAcompanhamentosByDataQuery(data), cancellationToken);
            return Results.Ok(result);
        })
        .WithName("GetCardapioComAcompanhamentosByData")
        .WithTags("Cardápios")
        .RequireAuthorization()
        .Produces<OperationResult<List<CardapioComAcompanhamentosResponse>>>(StatusCodes.Status200OK)
        .Produces(StatusCodes.Status401Unauthorized);
    }
}
