using FluentValidation;
using Az.Cardapio.Domain.Enums;

namespace Az.Cardapio.Application.Commands.AcompanhamentoCommands.UpdateAcompanhamento;

public class UpdateAcompanhamentoCommandValidator : AbstractValidator<UpdateAcompanhamentoCommand>
{
    public UpdateAcompanhamentoCommandValidator()
    {
        RuleFor(x => x.IdAcompanhamento)
            .NotEmpty().WithMessage("Id do acompanhamento é obrigatório");

        RuleFor(x => x.Nome)
            .NotEmpty().WithMessage("Nome do acompanhamento é obrigatório")
            .NotNull().WithMessage("Nome do acompanhamento não pode ser nulo")
            .MaximumLength(100).WithMessage("Nome do acompanhamento não pode exceder 100 caracteres");

        RuleFor(x => x.Descricao)
            .NotEmpty().WithMessage("Descrição do acompanhamento é obrigatória")
            .NotNull().WithMessage("Descrição do acompanhamento não pode ser nula")
            .MaximumLength(500).WithMessage("Descrição do acompanhamento não pode exceder 500 caracteres");

        RuleFor(x => x.Tipo)
            .Must(tipo => Enum.IsDefined(typeof(TipoAcompanhamentoEnum), tipo))
            .WithMessage("Tipo de acompanhamento inválido");

        RuleFor(x => x.Ordem)
            .GreaterThan(0).WithMessage("Ordem do acompanhamento deve ser maior que zero");

        RuleFor(x => x.ImagemUrl)
            .Must(url => string.IsNullOrEmpty(url) || Uri.TryCreate(url, UriKind.Absolute, out _))
            .WithMessage("URL da imagem deve ser válida quando fornecida");
    }
}
