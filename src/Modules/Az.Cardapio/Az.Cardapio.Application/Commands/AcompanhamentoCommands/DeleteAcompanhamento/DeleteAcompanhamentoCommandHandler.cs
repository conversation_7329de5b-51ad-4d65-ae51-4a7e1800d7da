using MediatR;
using Az.Cardapio.Domain.IRepositories;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;

namespace Az.Cardapio.Application.Commands.AcompanhamentoCommands.DeleteAcompanhamento;

public class DeleteAcompanhamentoCommandHandler : IRequestHandler<DeleteAcompanhamentoCommand, OperationResult<bool>>
{
    private readonly IAcompanhamentoRepository _acompanhamentoRepository;
    private readonly IUnitOfWork<Az.Cardapio.Infrastructure.Context.CardapioDbContext> _unitOfWork;
    private readonly ICurrentUser _currentUser;

    public DeleteAcompanhamentoCommandHandler(
        IAcompanhamentoRepository acompanhamentoRepository,
        IUnitOfWork<Az.Cardapio.Infrastructure.Context.CardapioDbContext> unitOfWork,
        ICurrentUser currentUser)
    {
        _acompanhamentoRepository = acompanhamentoRepository;
        _unitOfWork = unitOfWork;
        _currentUser = currentUser;
    }

    public async Task<OperationResult<bool>> Handle(DeleteAcompanhamentoCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return OperationResult.Error<bool>(["Operação cancelada."]);

        var acompanhamento = await _acompanhamentoRepository.GetByIdAsync(request.IdAcompanhamento);
        if (acompanhamento == null)
            return OperationResult.Error<bool>(["Acompanhamento não encontrado."]);

        if (acompanhamento.Excluido)
            return OperationResult.Error<bool>(["Acompanhamento já está excluído."]);

        // Soft delete
        await _acompanhamentoRepository.DeleteAsync(acompanhamento, Guid.Parse(_currentUser.UserId!));
        await _unitOfWork.CommitAsync();

        return OperationResult.Result(true);
    }
}
