using MediatR;
using Az.Cardapio.Domain.Entities;
using Az.Cardapio.Domain.IRepositories;
using Az.Cardapio.Domain.Enums;
using Az.Shared.Shared.Common;
using Az.Shared.Shared.IRepositories;

namespace Az.Cardapio.Application.Commands.AcompanhamentoCommands.CreateAcompanhamento;

public class CreateAcompanhamentoCommandHandler : IRequestHandler<CreateAcompanhamentoCommand, OperationResult<Guid>>
{
    private readonly IAcompanhamentoRepository _acompanhamentoRepository;
    private readonly ICardapioRepository _cardapioRepository;
    private readonly IUnitOfWork<Az.Cardapio.Infrastructure.Context.CardapioDbContext> _unitOfWork;

    public CreateAcompanhamentoCommandHandler(
        IAcompanhamentoRepository acompanhamentoRepository,
        ICardapioRepository cardapioRepository,
        IUnitOfWork<Az.Cardapio.Infrastructure.Context.CardapioDbContext> unitOfWork)
    {
        _acompanhamentoRepository = acompanhamentoRepository;
        _cardapioRepository = cardapioRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<OperationResult<Guid>> Handle(CreateAcompanhamentoCommand request, CancellationToken cancellationToken)
    {
        if (cancellationToken.IsCancellationRequested)
            return OperationResult.Error<Guid>(["Operação cancelada."]);

        // Verificar se o cardápio existe
        var cardapio = await _cardapioRepository.GetByIdAsync(request.IdCardapio);
        if (cardapio == null)
            return OperationResult.Error<Guid>(["Cardápio não encontrado."]);

        // Verificar se já existe acompanhamento com a mesma ordem para este cardápio
        var acompanhamentoExistente = await _acompanhamentoRepository.GetByOrdemAndCardapioAsync(request.Ordem, request.IdCardapio);
        if (acompanhamentoExistente != null)
            return OperationResult.Error<Guid>(["Já existe um acompanhamento com esta ordem para este cardápio."]);

        // Criar o acompanhamento
        var acompanhamento = new Acompanhamento(
            request.IdCardapio,
            request.Nome,
            request.Descricao,
            (TipoAcompanhamentoEnum)request.Tipo,
            request.Ordem,
            request.ImagemUrl
        );

        await _acompanhamentoRepository.AddAsync(acompanhamento);
        await _unitOfWork.CommitAsync();

        return OperationResult.Result(acompanhamento.IdAcompanhamento);
    }
}
