using FluentValidation;
using Az.Cardapio.Domain.Enums;

namespace Az.Cardapio.Application.Commands.CardapioCommands.CreateCardapio;

public class CreateCardapioCommandValidator : AbstractValidator<CreateCardapioCommand>
{
    public CreateCardapioCommandValidator()
    {
        RuleFor(x => x.DataCardapio)
            .NotEqual(default(DateOnly)).WithMessage("Data do cardápio é obrigatória")
            .Must(data => data >= DateOnly.FromDateTime(DateTime.Today))
            .WithMessage("Data do cardápio não pode ser anterior à data atual");

        RuleFor(x => x.Titulo)
            .NotEmpty().WithMessage("Título é obrigatório")
            .NotNull().WithMessage("Título não pode ser nulo")
            .MaximumLength(200).WithMessage("Título não pode exceder 200 caracteres");

        RuleFor(x => x.Descricao)
            .NotEmpty().WithMessage("Descrição é obrigatória")
            .NotNull().WithMessage("Descrição não pode ser nula")
            .MaximumLength(1000).WithMessage("Descrição não pode exceder 1000 caracteres");

        RuleFor(x => x.Ordem)
            .GreaterThan(0).WithMessage("Ordem deve ser maior que zero");

        RuleFor(x => x.IdProteina)
            .NotEmpty().WithMessage("Id da proteína é obrigatório");

        RuleFor(x => x.DescricaoProteina)
            .NotEmpty().WithMessage("Descrição da proteína é obrigatória")
            .NotNull().WithMessage("Descrição da proteína não pode ser nula")
            .MaximumLength(200).WithMessage("Descrição da proteína não pode exceder 200 caracteres");

        RuleFor(x => x.ImagensUrls)
            .NotNull().WithMessage("Lista de imagens não pode ser nula");

        RuleForEach(x => x.ImagensUrls)
            .NotEmpty().WithMessage("URL da imagem não pode ser vazia")
            .Must(url => Uri.TryCreate(url, UriKind.Absolute, out _))
            .WithMessage("URL da imagem deve ser válida");

        // Validação dos acompanhamentos quando fornecidos
        When(x => x.Acompanhamentos != null && x.Acompanhamentos.Any(), () =>
        {
            RuleForEach(x => x.Acompanhamentos)
                .SetValidator(new CreateAcompanhamentoDtoValidator());
        });
    }
}

public class CreateAcompanhamentoDtoValidator : AbstractValidator<CreateAcompanhamentoDto>
{
    public CreateAcompanhamentoDtoValidator()
    {
        RuleFor(x => x.Nome)
            .NotEmpty().WithMessage("Nome do acompanhamento é obrigatório")
            .MaximumLength(100).WithMessage("Nome do acompanhamento não pode exceder 100 caracteres");

        RuleFor(x => x.Descricao)
            .NotEmpty().WithMessage("Descrição do acompanhamento é obrigatória")
            .MaximumLength(500).WithMessage("Descrição do acompanhamento não pode exceder 500 caracteres");

        RuleFor(x => x.Tipo)
            .Must(tipo => Enum.IsDefined(typeof(TipoAcompanhamentoEnum), tipo))
            .WithMessage("Tipo de acompanhamento inválido");

        RuleFor(x => x.Ordem)
            .GreaterThan(0).WithMessage("Ordem do acompanhamento deve ser maior que zero");

        RuleFor(x => x.ImagemUrl)
            .Must(url => string.IsNullOrEmpty(url) || Uri.TryCreate(url, UriKind.Absolute, out _))
            .WithMessage("URL da imagem deve ser válida quando fornecida");
    }
}
