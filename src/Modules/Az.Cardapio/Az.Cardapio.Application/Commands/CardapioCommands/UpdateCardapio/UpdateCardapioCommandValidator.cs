using FluentValidation;

namespace Az.Cardapio.Application.Commands.CardapioCommands.UpdateCardapio;

public class UpdateCardapioCommandValidator : AbstractValidator<UpdateCardapioCommand>
{
    public UpdateCardapioCommandValidator()
    {
        RuleFor(x => x.IdCardapio)
            .NotEmpty().WithMessage("Id do cardápio é obrigatório");

        RuleFor(x => x.DataCardapio)
            .NotEqual(default(DateOnly)).WithMessage("Data do cardápio é obrigatória")
            .Must(data => data >= DateOnly.FromDateTime(DateTime.Today))
            .WithMessage("Data do cardápio não pode ser anterior à data atual");

        RuleFor(x => x.Titulo)
            .NotEmpty().WithMessage("Título é obrigatório")
            .NotNull().WithMessage("Título não pode ser nulo")
            .MaximumLength(200).WithMessage("Título não pode exceder 200 caracteres");

        RuleFor(x => x.Desc<PERSON>o)
            .NotEmpty().WithMessage("Descrição é obrigatória")
            .NotNull().WithMessage("Descrição não pode ser nula")
            .MaximumLength(1000).WithMessage("Descrição não pode exceder 1000 caracteres");

        RuleFor(x => x.Ordem)
            .GreaterThan(0).WithMessage("Ordem deve ser maior que zero");

        RuleFor(x => x.IdProteina)
            .NotEmpty().WithMessage("Id da proteína é obrigatório");

        RuleFor(x => x.DescricaoProteina)
            .NotEmpty().WithMessage("Descrição da proteína é obrigatória")
            .NotNull().WithMessage("Descrição da proteína não pode ser nula")
            .MaximumLength(200).WithMessage("Descrição da proteína não pode exceder 200 caracteres");

        RuleFor(x => x.ImagensUrls)
            .NotNull().WithMessage("Lista de imagens não pode ser nula");

        RuleForEach(x => x.ImagensUrls)
            .NotEmpty().WithMessage("URL da imagem não pode ser vazia")
            .Must(url => Uri.TryCreate(url, UriKind.Absolute, out _))
            .WithMessage("URL da imagem deve ser válida");
    }
}
