using Az.Shared.Shared.Services.Interfaces;
using Microsoft.Extensions.Logging;

namespace Az.Cardapio.Application.Services;

public interface ICardapioCacheInvalidationService
{
    Task InvalidateCardapiosCacheAsync(CancellationToken cancellationToken = default);
    Task InvalidateCardapioCacheByDataAsync(DateOnly data, CancellationToken cancellationToken = default);
    Task InvalidateAllCardapiosCacheAsync(CancellationToken cancellationToken = default);
}

public class CardapioCacheInvalidationService : ICardapioCacheInvalidationService
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<CardapioCacheInvalidationService> _logger;

    public CardapioCacheInvalidationService(
        ICacheService cacheService,
        ILogger<CardapioCacheInvalidationService> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task InvalidateCardapiosCacheAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Invalidando cache de cardápios...");
            
            // Invalidar todas as chaves relacionadas a cardápios
            await _cacheService.RemoveByPatternAsync("azfood:cardapio:*");
            
            _logger.LogInformation("Cache de cardápios invalidado com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao invalidar cache de cardápios");
            // Não propagar a exceção para não quebrar o fluxo principal
        }
    }

    public async Task InvalidateCardapioCacheByDataAsync(DateOnly data, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Invalidando cache de cardápios para data {Data}...", data);
            
            // Invalidar chaves específicas da data
            var cacheKey = $"azfood:cardapio:cardapios-com-acompanhamentos:data:{data:yyyy-MM-dd}";
            await _cacheService.RemoveAsync(cacheKey);
            
            _logger.LogInformation("Cache de cardápios para data {Data} invalidado com sucesso", data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao invalidar cache de cardápios para data {Data}", data);
            // Não propagar a exceção para não quebrar o fluxo principal
        }
    }

    public async Task InvalidateAllCardapiosCacheAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Invalidando todos os caches do módulo Cardápio...");
            
            // Invalidar todos os caches do módulo
            await InvalidateCardapiosCacheAsync(cancellationToken);
            
            _logger.LogInformation("Todos os caches do módulo Cardápio invalidados com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao invalidar todos os caches do módulo Cardápio");
            // Não propagar a exceção para não quebrar o fluxo principal
        }
    }
}
