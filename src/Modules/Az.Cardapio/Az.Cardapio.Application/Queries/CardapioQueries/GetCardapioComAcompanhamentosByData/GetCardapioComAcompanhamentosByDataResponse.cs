using Az.Cardapio.Domain.Enums;

namespace Az.Cardapio.Application.Queries.CardapioQueries.GetCardapioComAcompanhamentosByData;

public record GetCardapioComAcompanhamentosByDataResponse(
    Guid IdCardapio,
    string Titulo,
    string Descricao,
    Guid IdProteina,
    int Ordem,
    List<string> ImagensUrls,
    bool Lowcarb,
    List<AcompanhamentoDto> Acompanhamentos
);

public record AcompanhamentoDto(
    Guid IdAcompanhamento,
    string Nome,
    string Descricao,
    TipoAcompanhamentoEnum Tipo,
    int Ordem,
    string? ImagemUrl
);
