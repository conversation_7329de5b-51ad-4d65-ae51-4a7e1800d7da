using MediatR;
using Az.Cardapio.Domain.IRepositories;
using Az.Shared.Shared.Common;

namespace Az.Cardapio.Application.Queries.AcompanhamentoQueries.GetAcompanhamentosByCardapioId;

public class GetAcompanhamentosByCardapioIdQueryHandler : IRequestHandler<GetAcompanhamentosByCardapioIdQuery, OperationResult<List<GetAcompanhamentosByCardapioIdResponse>>>
{
    private readonly IAcompanhamentoRepository _acompanhamentoRepository;

    public GetAcompanhamentosByCardapioIdQueryHandler(IAcompanhamentoRepository acompanhamentoRepository)
    {
        _acompanhamentoRepository = acompanhamentoRepository;
    }

    public async Task<OperationResult<List<GetAcompanhamentosByCardapioIdResponse>>> Handle(GetAcompanhamentosByCardapioIdQuery request, CancellationToken cancellationToken)
    {
        var acompanhamentos = await _acompanhamentoRepository.GetByCardapioIdAsync(request.IdCardapio);

        var response = acompanhamentos
            .OrderBy(a => a.Ordem)
            .Select(a => new GetAcompanhamentosByCardapioIdResponse(
                a.IdAcompanhamento,
                a.Nome,
                a.Descricao,
                a.Tipo,
                a.Ordem,
                a.ImagemUrl,
                a.Ativo
            ))
            .ToList();

        return OperationResult.Result(response);
    }
}
