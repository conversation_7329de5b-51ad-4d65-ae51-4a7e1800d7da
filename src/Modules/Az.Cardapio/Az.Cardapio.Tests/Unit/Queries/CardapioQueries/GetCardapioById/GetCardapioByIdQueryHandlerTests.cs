using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Cardapio.Application.Queries.CardapioQueries.GetCardapioById;
using Az.Cardapio.Domain.IRepositories;

namespace Az.Cardapio.Tests.Unit.Queries.CardapioQueries.GetCardapioById;

public class GetCardapioByIdQueryHandlerTests
{
    private readonly Mock<ICardapioRepository> _cardapioRepoMock;
    private readonly GetCardapioByIdQueryHandler _handler;

    public GetCardapioByIdQueryHandlerTests()
    {
        _cardapioRepoMock = new Mock<ICardapioRepository>();
        _handler = new GetCardapioByIdQueryHandler(_cardapioRepoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar cardápio por ID com sucesso")]
    public async Task Deve_Retornar_Cardapio_Por_Id_Com_Sucesso()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var idProteina = Guid.NewGuid();
        var dataCardapio = DateOnly.FromDateTime(DateTime.Today.AddDays(1));
        var imagensUrls = new List<string> { "https://example.com/image1.jpg", "https://example.com/image2.jpg" };

        var cardapioExistente = new Domain.Entities.Cardapio(
            dataCardapio,
            "Cardápio Especial",
            "Descrição do cardápio especial",
            1,
            idProteina,
            "Frango Grelhado",
            imagensUrls,
            true
        );

        var query = new GetCardapioByIdQuery(cardapioId);

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(cardapioExistente.IdCardapio, result.Data.IdCardapio);
        Assert.Equal(cardapioExistente.DataCardapio, result.Data.DataCardapio);
        Assert.Equal(cardapioExistente.Titulo, result.Data.Titulo);
        Assert.Equal(cardapioExistente.Descricao, result.Data.Descricao);
        Assert.Equal(cardapioExistente.Ordem, result.Data.Ordem);
        Assert.Equal(cardapioExistente.IdProteina, result.Data.IdProteina);
        Assert.Equal(cardapioExistente.DescricaoProteina, result.Data.DescricaoProteina);
        Assert.Equal(cardapioExistente.ImagensUrls, result.Data.ImagensUrls);
        Assert.Equal(cardapioExistente.Lowcarb, result.Data.Lowcarb);
        Assert.Equal(cardapioExistente.Ativo, result.Data.Ativo);

        _cardapioRepoMock.Verify(r => r.GetByIdAsync(cardapioId), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se cardápio não encontrado")]
    public async Task Deve_Retornar_Erro_Se_Cardapio_Nao_Encontrado()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var query = new GetCardapioByIdQuery(cardapioId);

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync((Domain.Entities.Cardapio)null!);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Cardápio não encontrado.", result.Messages!);
        Assert.Null(result.Data);

        _cardapioRepoMock.Verify(r => r.GetByIdAsync(cardapioId), Times.Once);
    }

    [Fact(DisplayName = "Deve mapear corretamente todos os campos do cardápio")]
    public async Task Deve_Mapear_Corretamente_Todos_Os_Campos()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var idProteina = Guid.NewGuid();
        var dataCardapio = DateOnly.FromDateTime(DateTime.Today.AddDays(5));
        var imagensUrls = new List<string> 
        { 
            "https://example.com/image1.jpg", 
            "https://example.com/image2.jpg",
            "https://example.com/image3.jpg"
        };

        var cardapioExistente = new Domain.Entities.Cardapio(
            dataCardapio,
            "Cardápio Premium",
            "Cardápio com ingredientes premium selecionados",
            3,
            idProteina,
            "Salmão Grelhado com Ervas",
            imagensUrls,
            false // Não é low carb
        );

        var query = new GetCardapioByIdQuery(cardapioId);

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        
        // Verificar todos os campos específicos
        Assert.Equal("Cardápio Premium", result.Data.Titulo);
        Assert.Equal("Cardápio com ingredientes premium selecionados", result.Data.Descricao);
        Assert.Equal(3, result.Data.Ordem);
        Assert.Equal("Salmão Grelhado com Ervas", result.Data.DescricaoProteina);
        Assert.False(result.Data.Lowcarb);
        Assert.Equal(3, result.Data.ImagensUrls.Count);
        Assert.Contains("https://example.com/image1.jpg", result.Data.ImagensUrls);
        Assert.Contains("https://example.com/image2.jpg", result.Data.ImagensUrls);
        Assert.Contains("https://example.com/image3.jpg", result.Data.ImagensUrls);
    }
}
