using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Cardapio.Application.Queries.AcompanhamentoQueries.GetAcompanhamentosByCardapioId;
using Az.Cardapio.Domain.IRepositories;
using Az.Cardapio.Domain.Enums;

namespace Az.Cardapio.Tests.Unit.Queries.AcompanhamentoQueries.GetAcompanhamentosByCardapioId;

public class GetAcompanhamentosByCardapioIdQueryHandlerTests
{
    private readonly Mock<IAcompanhamentoRepository> _acompanhamentoRepoMock;
    private readonly GetAcompanhamentosByCardapioIdQueryHandler _handler;

    public GetAcompanhamentosByCardapioIdQueryHandlerTests()
    {
        _acompanhamentoRepoMock = new Mock<IAcompanhamentoRepository>();
        _handler = new GetAcompanhamentosByCardapioIdQueryHandler(_acompanhamentoRepoMock.Object);
    }

    [Fact(DisplayName = "Deve retornar acompanhamentos ordenados por ordem")]
    public async Task Deve_Retornar_Acompanhamentos_Ordenados_Por_Ordem()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var query = new GetAcompanhamentosByCardapioIdQuery(cardapioId);

        var acompanhamentos = new List<Domain.Entities.Acompanhamento>
        {
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Acompanhamento C",
                "Descrição C",
                TipoAcompanhamentoEnum.Bebida,
                3,
                "https://example.com/c.jpg"
            ),
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Acompanhamento A",
                "Descrição A",
                TipoAcompanhamentoEnum.Salada,
                1,
                "https://example.com/a.jpg"
            ),
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Acompanhamento B",
                "Descrição B",
                TipoAcompanhamentoEnum.Salada,
                2
            )
        };

        _acompanhamentoRepoMock.Setup(r => r.GetByCardapioIdAsync(cardapioId))
            .ReturnsAsync(acompanhamentos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Equal(3, result.Data.Count);

        // Verificar se está ordenado por ordem
        Assert.Equal(1, result.Data[0].Ordem);
        Assert.Equal(2, result.Data[1].Ordem);
        Assert.Equal(3, result.Data[2].Ordem);

        // Verificar mapeamento correto
        Assert.Equal("Acompanhamento A", result.Data[0].Nome);
        Assert.Equal("Descrição A", result.Data[0].Descricao);
        Assert.Equal(TipoAcompanhamentoEnum.Salada, result.Data[0].Tipo);
        Assert.Equal("https://example.com/a.jpg", result.Data[0].ImagemUrl);

        Assert.Equal("Acompanhamento B", result.Data[1].Nome);
        Assert.Equal("Descrição B", result.Data[1].Descricao);
        Assert.Equal(TipoAcompanhamentoEnum.Salada, result.Data[1].Tipo);
        Assert.Null(result.Data[1].ImagemUrl);

        Assert.Equal("Acompanhamento C", result.Data[2].Nome);
        Assert.Equal("Descrição C", result.Data[2].Descricao);
        Assert.Equal(TipoAcompanhamentoEnum.Bebida, result.Data[2].Tipo);
        Assert.Equal("https://example.com/c.jpg", result.Data[2].ImagemUrl);

        _acompanhamentoRepoMock.Verify(r => r.GetByCardapioIdAsync(cardapioId), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar lista vazia se não houver acompanhamentos")]
    public async Task Deve_Retornar_Lista_Vazia_Se_Nao_Houver_Acompanhamentos()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var query = new GetAcompanhamentosByCardapioIdQuery(cardapioId);

        _acompanhamentoRepoMock.Setup(r => r.GetByCardapioIdAsync(cardapioId))
            .ReturnsAsync(new List<Domain.Entities.Acompanhamento>());

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.NotNull(result.Data);
        Assert.Empty(result.Data);

        _acompanhamentoRepoMock.Verify(r => r.GetByCardapioIdAsync(cardapioId), Times.Once);
    }

    [Fact(DisplayName = "Deve mapear corretamente todos os campos dos acompanhamentos")]
    public async Task Deve_Mapear_Corretamente_Todos_Os_Campos()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var acompanhamentoId = Guid.NewGuid();
        var query = new GetAcompanhamentosByCardapioIdQuery(cardapioId);

        var acompanhamento = new Domain.Entities.Acompanhamento(
            cardapioId,
            "Salada Caesar Premium",
            "Salada caesar com croutons artesanais e molho especial",
            TipoAcompanhamentoEnum.Salada,
            1,
            "https://example.com/salada-caesar.jpg"
        );

        _acompanhamentoRepoMock.Setup(r => r.GetByCardapioIdAsync(cardapioId))
            .ReturnsAsync(new List<Domain.Entities.Acompanhamento> { acompanhamento });

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Single(result.Data);

        var acompanhamentoResponse = result.Data.First();
        Assert.Equal(acompanhamento.IdAcompanhamento, acompanhamentoResponse.IdAcompanhamento);
        Assert.Equal("Salada Caesar Premium", acompanhamentoResponse.Nome);
        Assert.Equal("Salada caesar com croutons artesanais e molho especial", acompanhamentoResponse.Descricao);
        Assert.Equal(TipoAcompanhamentoEnum.Salada, acompanhamentoResponse.Tipo);
        Assert.Equal(1, acompanhamentoResponse.Ordem);
        Assert.Equal("https://example.com/salada-caesar.jpg", acompanhamentoResponse.ImagemUrl);
        Assert.Equal(acompanhamento.Ativo, acompanhamentoResponse.Ativo);
    }

    [Fact(DisplayName = "Deve retornar acompanhamentos de diferentes tipos")]
    public async Task Deve_Retornar_Acompanhamentos_De_Diferentes_Tipos()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var query = new GetAcompanhamentosByCardapioIdQuery(cardapioId);

        var acompanhamentos = new List<Domain.Entities.Acompanhamento>
        {
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Salada Verde",
                "Salada com folhas verdes",
                TipoAcompanhamentoEnum.Salada,
                1
            ),
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Refrigerante",
                "Coca-Cola 350ml",
                TipoAcompanhamentoEnum.Bebida,
                2
            ),
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Salada de Frutas",
                "Mix de frutas da estação",
                TipoAcompanhamentoEnum.Salada,
                3
            ),
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Suco Natural",
                "Suco de laranja natural",
                TipoAcompanhamentoEnum.Bebida,
                4
            )
        };

        _acompanhamentoRepoMock.Setup(r => r.GetByCardapioIdAsync(cardapioId))
            .ReturnsAsync(acompanhamentos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(4, result.Data.Count);

        // Verificar tipos
        Assert.Equal(TipoAcompanhamentoEnum.Salada, result.Data[0].Tipo);
        Assert.Equal(TipoAcompanhamentoEnum.Bebida, result.Data[1].Tipo);
        Assert.Equal(TipoAcompanhamentoEnum.Salada, result.Data[2].Tipo);
        Assert.Equal(TipoAcompanhamentoEnum.Bebida, result.Data[3].Tipo);

        // Verificar nomes
        Assert.Equal("Salada Verde", result.Data[0].Nome);
        Assert.Equal("Refrigerante", result.Data[1].Nome);
        Assert.Equal("Salada de Frutas", result.Data[2].Nome);
        Assert.Equal("Suco Natural", result.Data[3].Nome);
    }

    [Fact(DisplayName = "Deve retornar acompanhamentos com e sem imagem")]
    public async Task Deve_Retornar_Acompanhamentos_Com_E_Sem_Imagem()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var query = new GetAcompanhamentosByCardapioIdQuery(cardapioId);

        var acompanhamentos = new List<Domain.Entities.Acompanhamento>
        {
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Com Imagem",
                "Descrição",
                TipoAcompanhamentoEnum.Salada,
                1,
                "https://example.com/imagem.jpg"
            ),
            new Domain.Entities.Acompanhamento(
                cardapioId,
                "Sem Imagem",
                "Descrição",
                TipoAcompanhamentoEnum.Bebida,
                2
                // Sem ImagemUrl
            )
        };

        _acompanhamentoRepoMock.Setup(r => r.GetByCardapioIdAsync(cardapioId))
            .ReturnsAsync(acompanhamentos);

        // Act
        var result = await _handler.Handle(query, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.Equal(2, result.Data.Count);

        Assert.Equal("https://example.com/imagem.jpg", result.Data[0].ImagemUrl);
        Assert.Null(result.Data[1].ImagemUrl);
    }
}
