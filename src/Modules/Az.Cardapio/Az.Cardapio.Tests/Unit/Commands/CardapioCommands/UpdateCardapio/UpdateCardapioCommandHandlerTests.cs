using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Cardapio.Application.Commands.CardapioCommands.UpdateCardapio;
using Az.Cardapio.Domain.IRepositories;
using Az.Cardapio.Infrastructure.Context;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;

namespace Az.Cardapio.Tests.Unit.Commands.CardapioCommands.UpdateCardapio;

public class UpdateCardapioCommandHandlerTests
{
    private readonly Mock<ICardapioRepository> _cardapioRepoMock;
    private readonly Mock<IUnitOfWork<CardapioDbContext>> _uowMock;
    private readonly Mock<ICurrentUser> _currentUserMock;
    private readonly UpdateCardapioCommandHandler _handler;

    public UpdateCardapioCommandHandlerTests()
    {
        _cardapioRepoMock = new Mock<ICardapioRepository>();
        _uowMock = new Mock<IUnitOfWork<CardapioDbContext>>();
        _currentUserMock = new Mock<ICurrentUser>();
        _handler = new UpdateCardapioCommandHandler(
            _cardapioRepoMock.Object,
            _uowMock.Object,
            _currentUserMock.Object);
    }

    [Fact(DisplayName = "Deve atualizar cardápio com sucesso")]
    public async Task Deve_Atualizar_Cardapio_Com_Sucesso()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var cardapioExistente = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Original",
            "Descrição Original",
            1,
            Guid.NewGuid(),
            "Proteína Original",
            new List<string> { "https://example.com/original.jpg" },
            false
        );

        var command = new UpdateCardapioCommand(
            cardapioId,
            DateOnly.FromDateTime(DateTime.Today.AddDays(2)),
            "Título Atualizado",
            "Descrição Atualizada",
            2,
            Guid.NewGuid(),
            "Proteína Atualizada",
            new List<string> { "https://example.com/updated.jpg" },
            true
        );

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);
        _cardapioRepoMock.Setup(r => r.GetByTituloAndDataAsync(command.Titulo, command.DataCardapio))
            .ReturnsAsync((Domain.Entities.Cardapio)null!);
        _cardapioRepoMock.Setup(r => r.GetByOrdemAndDataAsync(command.Ordem, command.DataCardapio))
            .ReturnsAsync((Domain.Entities.Cardapio)null!);
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.True(result.Data);
        _cardapioRepoMock.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.Cardapio>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se cardápio não encontrado")]
    public async Task Deve_Retornar_Erro_Se_Cardapio_Nao_Encontrado()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var command = new UpdateCardapioCommand(
            cardapioId,
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Atualizado",
            "Descrição Atualizada",
            1,
            Guid.NewGuid(),
            "Proteína Atualizada",
            new List<string> { "https://example.com/updated.jpg" },
            true
        );

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync((Domain.Entities.Cardapio)null!);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Cardápio não encontrado.", result.Messages!);
        _cardapioRepoMock.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.Cardapio>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve retornar erro se título já existe para outro cardápio na mesma data")]
    public async Task Deve_Retornar_Erro_Se_Titulo_Ja_Existe_Para_Outro_Cardapio()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var outroCardapioId = Guid.NewGuid();
        
        var cardapioExistente = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Original",
            "Descrição Original",
            1,
            Guid.NewGuid(),
            "Proteína Original",
            new List<string>(),
            false
        );

        var outroCardapio = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Conflitante",
            "Descrição",
            2,
            Guid.NewGuid(),
            "Proteína",
            new List<string>(),
            false
        );

        var command = new UpdateCardapioCommand(
            cardapioId,
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Conflitante", // Mesmo título do outro cardápio
            "Descrição Atualizada",
            1,
            Guid.NewGuid(),
            "Proteína Atualizada",
            new List<string> { "https://example.com/updated.jpg" },
            true
        );

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);
        _cardapioRepoMock.Setup(r => r.GetByTituloAndDataAsync(command.Titulo, command.DataCardapio))
            .ReturnsAsync(outroCardapio);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Já existe outro cardápio com este título para a data especificada.", result.Messages!);
        _cardapioRepoMock.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.Cardapio>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve retornar erro se ordem já existe para outro cardápio na mesma data")]
    public async Task Deve_Retornar_Erro_Se_Ordem_Ja_Existe_Para_Outro_Cardapio()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var outroCardapioId = Guid.NewGuid();
        
        var cardapioExistente = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Original",
            "Descrição Original",
            1,
            Guid.NewGuid(),
            "Proteína Original",
            new List<string>(),
            false
        );

        var outroCardapio = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Outro Título",
            "Descrição",
            2,
            Guid.NewGuid(),
            "Proteína",
            new List<string>(),
            false
        );

        var command = new UpdateCardapioCommand(
            cardapioId,
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Atualizado",
            "Descrição Atualizada",
            2, // Mesma ordem do outro cardápio
            Guid.NewGuid(),
            "Proteína Atualizada",
            new List<string> { "https://example.com/updated.jpg" },
            true
        );

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);
        _cardapioRepoMock.Setup(r => r.GetByTituloAndDataAsync(command.Titulo, command.DataCardapio))
            .ReturnsAsync((Domain.Entities.Cardapio)null!);
        _cardapioRepoMock.Setup(r => r.GetByOrdemAndDataAsync(command.Ordem, command.DataCardapio))
            .ReturnsAsync(outroCardapio);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Já existe outro cardápio com esta ordem para a data especificada.", result.Messages!);
        _cardapioRepoMock.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.Cardapio>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve permitir atualizar com mesmo título e ordem do próprio cardápio")]
    public async Task Deve_Permitir_Atualizar_Com_Mesmo_Titulo_E_Ordem_Do_Proprio_Cardapio()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var cardapioExistente = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Original",
            "Descrição Original",
            1,
            Guid.NewGuid(),
            "Proteína Original",
            new List<string>(),
            false
        );

        // Usar reflection para definir o ID do cardápio
        var idProperty = typeof(Az.Cardapio.Domain.Entities.Cardapio).GetProperty("IdCardapio");
        idProperty?.SetValue(cardapioExistente, cardapioId);

        var command = new UpdateCardapioCommand(
            cardapioId,
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Título Original", // Mesmo título
            "Descrição Atualizada",
            1, // Mesma ordem
            Guid.NewGuid(),
            "Proteína Atualizada",
            new List<string> { "https://example.com/updated.jpg" },
            true
        );

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);
        _cardapioRepoMock.Setup(r => r.GetByTituloAndDataAsync(command.Titulo, command.DataCardapio))
            .ReturnsAsync(cardapioExistente); // Retorna o próprio cardápio
        _cardapioRepoMock.Setup(r => r.GetByOrdemAndDataAsync(command.Ordem, command.DataCardapio))
            .ReturnsAsync(cardapioExistente); // Retorna o próprio cardápio
        _currentUserMock.Setup(u => u.UserId).Returns(Guid.NewGuid().ToString());
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.True(result.Data);
        _cardapioRepoMock.Verify(r => r.UpdateAsync(It.IsAny<Domain.Entities.Cardapio>()), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }
}
