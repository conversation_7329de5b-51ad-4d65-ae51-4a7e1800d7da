using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Xunit;
using Moq;
using Az.Cardapio.Application.Commands.CardapioCommands.DeleteCardapio;
using Az.Cardapio.Domain.IRepositories;
using Az.Cardapio.Infrastructure.Context;
using Az.Shared.Shared.IRepositories;
using Az.Shared.Shared.Services.Interfaces;

namespace Az.Cardapio.Tests.Unit.Commands.CardapioCommands.DeleteCardapio;

public class DeleteCardapioCommandHandlerTests
{
    private readonly Mock<ICardapioRepository> _cardapioRepoMock;
    private readonly Mock<IUnitOfWork<CardapioDbContext>> _uowMock;
    private readonly Mock<ICurrentUser> _currentUserMock;
    private readonly DeleteCardapioCommandHandler _handler;

    public DeleteCardapioCommandHandlerTests()
    {
        _cardapioRepoMock = new Mock<ICardapioRepository>();
        _uowMock = new Mock<IUnitOfWork<CardapioDbContext>>();
        _currentUserMock = new Mock<ICurrentUser>();
        _handler = new DeleteCardapioCommandHandler(
            _cardapioRepoMock.Object,
            _uowMock.Object,
            _currentUserMock.Object);
    }

    [Fact(DisplayName = "Deve excluir cardápio com sucesso")]
    public async Task Deve_Excluir_Cardapio_Com_Sucesso()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var cardapioExistente = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Cardápio Teste",
            "Descrição do cardápio teste",
            1,
            Guid.NewGuid(),
            "Proteína Teste",
            new List<string> { "https://example.com/image.jpg" },
            true
        );

        var command = new DeleteCardapioCommand(cardapioId);

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);
        _currentUserMock.Setup(u => u.UserId).Returns(userId.ToString());
        _uowMock.Setup(u => u.CommitAsync()).Returns(Task.FromResult(1));

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.True(result.Data);
        _cardapioRepoMock.Verify(r => r.DeleteAsync(cardapioExistente, userId), Times.Once);
        _uowMock.Verify(u => u.CommitAsync(), Times.Once);
    }

    [Fact(DisplayName = "Deve retornar erro se cardápio não encontrado")]
    public async Task Deve_Retornar_Erro_Se_Cardapio_Nao_Encontrado()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var command = new DeleteCardapioCommand(cardapioId);

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync((Domain.Entities.Cardapio)null!);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Cardápio não encontrado.", result.Messages!);
        _cardapioRepoMock.Verify(r => r.DeleteAsync(It.IsAny<Domain.Entities.Cardapio>(), It.IsAny<Guid>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve retornar erro se cardápio já está excluído")]
    public async Task Deve_Retornar_Erro_Se_Cardapio_Ja_Excluido()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var cardapioExistente = new Domain.Entities.Cardapio(
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            "Cardápio Teste",
            "Descrição do cardápio teste",
            1,
            Guid.NewGuid(),
            "Proteína Teste",
            new List<string> { "https://example.com/image.jpg" },
            true
        );

        // Simular que o cardápio já foi excluído
        cardapioExistente.MarcarComoExcluido(userId);

        var command = new DeleteCardapioCommand(cardapioId);

        _cardapioRepoMock.Setup(r => r.GetByIdAsync(cardapioId)).ReturnsAsync(cardapioExistente);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Cardápio já está excluído.", result.Messages!);
        _cardapioRepoMock.Verify(r => r.DeleteAsync(It.IsAny<Domain.Entities.Cardapio>(), It.IsAny<Guid>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }

    [Fact(DisplayName = "Deve retornar erro se operação cancelada")]
    public async Task Deve_Retornar_Erro_Se_Operacao_Cancelada()
    {
        // Arrange
        var cardapioId = Guid.NewGuid();
        var command = new DeleteCardapioCommand(cardapioId);
        var cancellationToken = new CancellationToken(true);

        // Act
        var result = await _handler.Handle(command, cancellationToken);

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Operação cancelada.", result.Messages!);
        _cardapioRepoMock.Verify(r => r.GetByIdAsync(It.IsAny<Guid>()), Times.Never);
        _cardapioRepoMock.Verify(r => r.DeleteAsync(It.IsAny<Domain.Entities.Cardapio>(), It.IsAny<Guid>()), Times.Never);
        _uowMock.Verify(u => u.CommitAsync(), Times.Never);
    }
}
