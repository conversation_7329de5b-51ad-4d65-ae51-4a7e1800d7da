# <PERSON><PERSON><PERSON><PERSON>ste módulo é responsável pelo gerenciamento de cardápios e acompanhamentos do restaurante.

---

## Estrutura do Projeto

| Projeto | Descrição |
|---------|-----------|
| **Az.Cardapio.Domain** | Entidades de domínio e regras de negócio |
| **Az.Cardapio.Application** | Comandos, queries, DTOs e validações |
| **Az.Cardapio.Infrastructure** | Implementação de persistência e integrações |
| **Az.Cardapio.IntegrationEvents** | Eventos de integração para comunicação entre módulos |
| **Az.Cardapio.Api** | Endpoints e configurações específicas do módulo |
| **Az.Cardapio.Tests** | Testes unitários e de integração |

---

## Regras de Negócio

### Cardápio
- O cardápio deve ser gerado diariamente, não podendo ser criado para uma data anterior à data atual
- O título do cardápio deve ser único por data
- A ordem do cardápio deve ser única por data
- Cada cardápio pode ter vários acompanhamentos
- O cardápio deve ter a opção de low carb
- Campos obrigatórios: IdCardapio, DataCardapio, Titulo, Descricao, Ordem, IdProteina, DescricaoProteina, ImagensUrls, Lowcarb

### Acompanhamento
- Cada cardápio tem seus próprios acompanhamentos, não podendo ser compartilhado com outro cardápio
- O acompanhamento pode ser de vários tipos: Salada e Bebida
- A ordem do acompanhamento deve ser única por cardápio
- Campos obrigatórios: IdAcompanhamento, IdCardapio, Nome, Descricao, Tipo, Ordem

---

## Endpoints

### Cardápios

#### POST /v1/cardapios
- **Finalidade:** Cria um novo cardápio
- **Autorização:** Requerida
- **Body:** CreateCardapioCommand
- **Retorno:** OperationResult<Guid>

#### PUT /v1/cardapios/{id}
- **Finalidade:** Atualiza um cardápio existente
- **Autorização:** Requerida
- **Body:** UpdateCardapioCommand
- **Retorno:** OperationResult<bool>

#### DELETE /v1/cardapios/{id}
- **Finalidade:** Exclui um cardápio (soft delete)
- **Autorização:** Requerida
- **Retorno:** OperationResult<bool>

#### GET /v1/cardapios/{id}
- **Finalidade:** Retorna um cardápio pelo Id
- **Autorização:** Requerida
- **Retorno:** OperationResult<GetCardapioByIdResponse>

#### GET /v1/cardapios/resumidos?data={data}
- **Finalidade:** Retorna cardápios resumidos por data (título, ordem, descrição da proteína)
- **Autorização:** Requerida
- **Retorno:** OperationResult<List<GetCardapiosResumidosByDataResponse>>

#### GET /v1/cardapios/completos?data={data}
- **Finalidade:** Retorna cardápios completos com acompanhamentos por data (com cache)
- **Autorização:** Requerida
- **Retorno:** OperationResult<List<GetCardapioComAcompanhamentosByDataResponse>>

### Acompanhamentos

#### POST /v1/acompanhamentos
- **Finalidade:** Cria um novo acompanhamento
- **Autorização:** Requerida
- **Body:** CreateAcompanhamentoCommand
- **Retorno:** OperationResult<Guid>

#### PUT /v1/acompanhamentos/{id}
- **Finalidade:** Atualiza um acompanhamento existente
- **Autorização:** Requerida
- **Body:** UpdateAcompanhamentoCommand
- **Retorno:** OperationResult<bool>

#### DELETE /v1/acompanhamentos/{id}
- **Finalidade:** Exclui um acompanhamento (soft delete)
- **Autorização:** Requerida
- **Retorno:** OperationResult<bool>

#### GET /v1/acompanhamentos/cardapio/{idCardapio}
- **Finalidade:** Retorna acompanhamentos por Id do cardápio
- **Autorização:** Requerida
- **Retorno:** OperationResult<List<GetAcompanhamentosByCardapioIdResponse>>

---

## Sistema de Cache Redis

O módulo Cardápio utiliza cache Redis para otimizar consultas frequentes e melhorar a performance da aplicação.

### Estrutura de Chaves

As chaves de cache seguem um padrão hierárquico:
```
azfood:cardapio:{entidade}:{operacao}:{parametros}
```

**Exemplos:**
- `azfood:cardapio:cardapios-com-acompanhamentos:data:2024-01-15`

### TTL (Time To Live)

- **Cardápios com Acompanhamentos:** 1 dia (24 horas)

### Invalidação Automática

O cache é invalidado automaticamente nas seguintes operações:

#### Cardápios
- **Create:** Invalida cache da data específica
- **Update:** Invalida cache da data específica
- **Delete:** Invalida cache da data específica

#### Acompanhamentos
- **Create:** Invalida cache da data do cardápio relacionado
- **Update:** Invalida cache da data do cardápio relacionado
- **Delete:** Invalida cache da data do cardápio relacionado

---

## Enums

### TipoAcompanhamentoEnum
- **Salada** = 1
- **Bebida** = 2

---

## Migrations

Para criar uma nova migration no módulo Cardápio:

```bash
dotnet ef migrations add [MigrationName] --project src/Modules/Az.Cardapio/Az.Cardapio.Infrastructure --startup-project src/Api/Az.Api --context CardapioDbContext
```

---

## Regras de Implementação

- Todas as entidades devem estender de `BaseEntidade`
- Todos os repositórios devem estender de `IRepository<T>`
- Usar `private set` para propriedades de entidades
- Toda entidade deve ter métodos de alteração
- Não usar try-catch blocks no código da aplicação
- Entity mapping classes devem estender de `BaseEntidadeMap<T>`
- Usar soft delete para exclusões
- Implementar cache com invalidação automática em operações CRUD
