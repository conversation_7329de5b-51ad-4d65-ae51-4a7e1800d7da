using Az.Cardapio.Domain.Enums;
using Az.Shared.Shared.Entities;

namespace Az.Cardapio.Domain.Entities;

public class Acompanhamento : BaseEntidade
{
    public Guid IdAcompanhamento { get; private set; }
    public Guid IdCardapio { get; private set; }
    public string Nome { get; private set; }
    public string Descricao { get; private set; }
    public TipoAcompanhamentoEnum Tipo { get; private set; }
    public int Ordem { get; private set; }
    public string? ImagemUrl { get; private set; }

    // Navigation property
    public virtual Cardapio Cardapio { get; private set; } = null!;

    protected Acompanhamento()
    {
        Nome = string.Empty;
        Descricao = string.Empty;
    }

    public Acompanhamento(
        Guid idCardapio,
        string nome,
        string descricao,
        TipoAcompanhamentoEnum tipo,
        int ordem,
        string? imagemUrl = null)
    {
        IdAcompanhamento = Guid.NewGuid();
        IdCardapio = idCardapio;
        Nome = nome;
        Descricao = descricao;
        Tipo = tipo;
        Ordem = ordem;
        ImagemUrl = imagemUrl;
    }

    public void AlterarNome(string novoNome)
        => Nome = novoNome;

    public void AlterarDescricao(string novaDescricao)
        => Descricao = novaDescricao;

    public void AlterarTipo(TipoAcompanhamentoEnum novoTipo)
        => Tipo = novoTipo;

    public void AlterarOrdem(int novaOrdem)
        => Ordem = novaOrdem;

    public void AlterarImagemUrl(string? novaImagemUrl)
        => ImagemUrl = novaImagemUrl;
}
