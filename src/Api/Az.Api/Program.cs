using System.Reflection;
using Az.Accounts.Api.Configurations;
using Az.Administrativo.Api.Configurations;
using Az.Cardapio.Api.Configurations;
using Az.Upload.Api.Configurations;
using Az.Api.Configurations;
using Az.Shared.Shared.Services;
using Az.Shared.Shared.Services.Interfaces;
using FluentValidation;
using MediatR;

Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddEndpointsApiExplorer();
SwaggerConfig.AddSwaggerConfiguration(builder);
JwtConfig.Configure(builder.Services, builder.Configuration);

//Assembly
List<Assembly> mediatoRAssemblies = [typeof(Program).Assembly];

// Add modules
AccountModularExtension.AddModule(builder.Services, builder.Configuration, mediatoRAssemblies);
AdministrativoModularExtension.AddModule(builder.Services, builder.Configuration, mediatoRAssemblies);
CardapioModularExtension.AddModule(builder.Services, builder.Configuration, mediatoRAssemblies);
UploadModularExtension.AddModule(builder.Services, builder.Configuration, mediatoRAssemblies);

// Add MediatR
builder.Services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(mediatoRAssemblies.ToArray()));

// Add current user
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddScoped<ICurrentUser, AspNetCurrentUser>();

// Configuração do MassTransit para RabbitMQ
builder.Services.AddRabbitMQMassTransit(builder.Configuration);

// Add FluentValidation
builder.Services.AddValidatorsFromAssemblyContaining<Program>();
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));


// Middlewares e configuração do app
var app = builder.Build();

// Use core middlewares
ApiConfig.AddApiConfiguration(app);

// Apply modules
AccountConfigApp.Apply(app);
AdministrativoConfigApp.Apply(app);
CardapioConfigApp.Apply(app);
UploadConfigApp.Apply(app);

// Use validation exception middleware
app.UseValidationExceptionMiddleware();

app.Run();

public partial class Program { }
