<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <PreserveCompilationContext>true</PreserveCompilationContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MassTransit" Version="8.4.1" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.4.1" />
    <PackageReference Include="MediatR" Version="12.5.0" />    
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />    
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Scalar.AspNetCore" Version="2.4.7" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
    <PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="8.0.3" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />

    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.12.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="../../Modules/Az.Administrativo/Az.Administrativo.Api/Az.Administrativo.Api.csproj" />
    <ProjectReference Include="../../Modules\Az.Accounts\Az.Accounts.Api/Az.Accounts.Api.csproj" />
    <ProjectReference Include="../../Modules/Az.Cardapio/Az.Cardapio.Api/Az.Cardapio.Api.csproj" />
    <ProjectReference Include="../../Modules/Az.Upload/Az.Upload.Api/Az.Upload.Api.csproj" />  </ItemGroup>
</Project>
