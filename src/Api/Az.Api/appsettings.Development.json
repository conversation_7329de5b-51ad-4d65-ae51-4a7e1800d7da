{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=localhost,1434;Database=AzFood;User Id=sa;Password=**********;Trusted_Connection=false;TrustServerCertificate=true",
    "AzureBlobStorage": "DefaultEndpointsProtocol=https;AccountName=azfoodstorage;AccountKey=YOUR_ACCOUNT_KEY;EndpointSuffix=core.windows.net"
  },
  "Jwt": {
    "Secret": "3B232B0E003255D195F253CFA8D50C40",
    "ExpireHours": 1,
    "Issuer": "MyCode",
    "Audience": "MyCode",       
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.AspNetCore.Authentication": "Information",
      "Microsoft.AspNetCore.Authorization": "Information"
    }
  },
  "RabbitMQ": {
    "HostName": "localhost",
    "Port": 5672,
    "UserName": "guest",
    "Password": "guest",
    "Exchange": "azfood.events"
  },
  "Redis": {
    "ConnectionString": "localhost:6379"
  },
  "AzureBlobStorage": {
    "ContainerName": "azfood-images",
    "BaseUrl": "http://localhost:10000/devstoreaccount1"
  }
}
