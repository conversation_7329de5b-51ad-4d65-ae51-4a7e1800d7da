# TEMPLATE - README para Módulos

> **IMPORTANTE:** Este é um template obrigatório para documentação de módulos. 
> **SEMPRE** siga esta estrutura para manter a organização do projeto.
> **SEMPRE** Segue sempre as Diretrizes de Documentação. no Documento `DIRETRIZES-DOCUMENTACAO.md`.
> **SEMPRE** Crie uma nova seção para novas funcionalidades técnicas.
> **SEMPRE** Rode o migration

---

# Módulo [Nome]

Descrição breve do módulo e sua responsabilidade.

---

## Estrutura do Projeto

| Projeto | Descrição |
|---------|-----------|
| **Az.[Modulo].Domain** | Entidades de domínio e regras de negócio |
| **Az.[Modulo].Application** | Comandos, queries, DTOs e validações |
| **Az.[Modulo].Infrastructure** | Implementação de persistência e integrações |
| **Az.[Modulo].IntegrationEvents** | Eventos de integração para comunicação entre módulos |
| **Az.[Modulo].Api** | Endpoints e configurações específicas do módulo |
| **Az.[Modulo].Tests** | Testes unitários e de integração |

---

## Design Patterns e Técnicas Utilizadas

A aplicação utiliza os seguintes padrões e técnicas. Para detalhes, consulte as seções técnicas correspondentes:

- **Repository**: abstração do acesso a dados ([detalhes](#persistência-repository-e-unit-of-work))
- **Unit of Work**: controle transacional ([detalhes](#persistência-repository-e-unit-of-work))
- **CQRS + Mediator**: separação de comandos/queries e uso do MediatR
- **Factory**: criação de contextos/servidores para testes
- **Dependency Injection**: injeção de dependência nativa do .NET
- **Observer**: eventos de integração entre módulos
- **Result Pattern**: respostas padronizadas para operações ([detalhes](#operation-result-result-pattern))
- **Soft Delete**: marcação lógica de exclusão ([detalhes](#soft-delete))
- **Assembly Scanning (Scrutor)**: registro automático de dependências ([detalhes](#assembly-scanning-scrutor))

### Convenções de Nomenclatura
- **Entidades**: PascalCase (ex: `User`, `Order`)
- **Interfaces**: I + PascalCase (ex: `IUserRepository`)
- **Commands**: PascalCase + Command (ex: `CreateUserCommand`)
- **Queries**: PascalCase + Query (ex: `GetUserByIdQuery`)
- **Eventos**: PascalCase + Event (ex: `[Entidade]CreatedEvent`)
- **Validadores**: PascalCase + Validator (ex: `Create[Entidade]CommandValidator` ou  ')

## Configuração de cada Módulo 

Para configurar o módulo,  utilize as extensões criadas em `Az.[NomeDoModulo].Api.Configurations`.

### Adicionando as Configurações no Program.cs

```csharp
// Add Modules
...
[NomeDoModulo]ModularExtension.AddModule(builder.Services, builder.Configuration, mediatoRAssemblies);
...
    
// Apply modules
...
[NomeDoModulo]ConfigApp.Apply(app);
...
```

### Estrutura dos Arquivos de Configuração

- `Configurations/[NomeDoModulo]ConfigApp.cs`: Adicione configurações específicas do módulo.
- ```csharp
    public static void Apply(WebApplication app)
    {
        app.ApplyMigrations<[NomeDoModulo]DbContext>();
        app.Map[Entidade]();
        app.Map[Entidade]();
        // Adicione mais mapeamentos de endpoints aqui
    }
    ```
- `Configurations/[NomeDoModulo]oModularExtension.cs`: Adicione serviços e extensões do módulo.
- ```csharp
    public static class [NomeDoModulo]ModularExtension
    {
        public static IServiceCollection AddModule(this IServiceCollection services, IConfiguration configuration, List<Assembly> mediatoRAssemblies)
        {
            services.AddDbContext<[NomeDoModulo]DbContext>(options =>
            {
                options.UseSqlServer(configuration.GetConnectionString("[NomeDoModulo]"));
            });
    
            services.AddScoped<I[Entidade]Repository, [Entidade]Repository>();
            services.AddScoped<I[Entidade]Repository, [Entidade]Repository>();
            // Adicione mais serviços aqui
    
            // Assembly scanning
            var assemblies = new List<Assembly>();
            assemblies.Add(typeof([NomeDoModulo]ModularExtension).Assembly);
            assemblies.Add(typeof(Create[Entidade]Command).Assembly);
            services.Scan(scan => scan
                .FromAssemblies(assemblies.ToArray())
                .AddClasses(classes => classes.Where(type => type.Name.EndsWith("Service") && !type.IsInterface))
                .AsImplementedInterfaces()
                .WithScopedLifetime());
    
            return services;
        }
    }
    ```

Esses métodos facilitam a organização e modularização das configurações e serviços do seu projeto.

## Estrutura da API

```
MapEndpoints
  ├── Map[Entidade1]Endpoints
  ├── Map[Entidade2]Endpoints
  └── Map[Entidade3]Endpoints
```

### Configuração

- Cada map criado deve ser declarado no arquivo `[Modulo]ConfigApp`
- No endpoint deve ser adicionado o `RequireAuthorization`
- Configurar status codes de acordo com o retorno

---

## Estrutura da Application

```
src/Modules/Az.[Modulo]/Az.[Modulo].Application/
├── Commands/
│   ├── [Entidade]Commands/
│   │   ├── Create[Entidade]/
│   │   │   ├── Create[Entidade]Command.cs
│   │   │   ├── Create[Entidade]CommandHandler.cs
│   │   │   └── Create[Entidade]CommandValidator.cs
│   │   ├── Update[Entidade]/
│   │   └── Delete[Entidade]/
└── Queries/
    ├── [Entidade]Queries/
    │   ├── Get[Entidade]ById/
    │   │   ├── Get[Entidade]ByIdQuery.cs
    │   │   ├── Get[Entidade]ByIdQueryHandler.cs
    │   │   ├── Get[Entidade]ByIdQueryValidator.cs
    │   │   └── Get[Entidade]ByIdResponse.cs
    │   └── GetAll[Entidades]/
```

### Regras de Organização

- Todos os comandos de uma entidade ficam em `Commands/[Entidade]Commands/`
- Todos os queries de uma entidade ficam em `Queries/[Entidade]Queries/`
- Todas as queries devem ter seu próprio response
- Cada operação (Create, Update, etc.) tem sua própria subpasta
- **Todas as queries com parâmetros devem ter um validator** - mesmo que seja apenas para validar um ID

---

## Estrutura da Domain

```
Domain/
├── Entities/
│   ├── [Entidade1].cs
│   └── [Entidade2].cs
├── Enums/
│   └── [Enum].cs
├── Structs/
│   └── [Struct].cs
└── IRepositories/
    └── I[Entidade]Repository.cs
```

### Regras de Implementação

- Todos os repositórios devem estender de `IRepository`
- Todas as entidades devem estender de `BaseEntidade`
- Usar `private set` para propriedades de entidades
- Toda entidade deve ter métodos de alteração (alguns já existem na `BaseEntidade`)

---

## Estrutura da Infrastructure

```
Infrastructure/
├── Mappings/
│   ├── [Entidade1]Map.cs
│   └── [Entidade2]Map.cs
└── Repositories/
    └── [Entidade]Repository.cs
```

#### Exemplo de Map:
```csharp
public class EntidadeMap : BaseEntidadeMap<Domain.Entities.NomeEntidade>
{
    public override void Configure(EntityTypeBuilder<Domain.Entities.NomeEntidade> builder)
    {
        base.Configure(builder); // ⚠️ OBRIGATÓRIO - Mapeia campos herdados de BaseEntidade

        // Configurações específicas da entidade
        builder.ToTable("NomeEntidade");
        builder.HasKey(c => c.IdEntidade);
        // ... outras configurações
    }
}
```
### Regras de Implementação

- Todas as entidades devem ter seu respectivo map
- Todos os repositórios devem estender de `Repository<'[Entidade]', [Modulo]DbContext>`, `'I[Entidade]Repository'`
- Os métodos de repositório devem ser assíncronos
- Os métodos de repositório devem ser públicos

---



## Estrutura da Testes

```
Tests/
└── Unit/
    ├── Commands/   
    │   └── [Entidade]Commands/
    │       ├── Create[Entidade]/
    │       │   ├── Create[Entidade]CommandHandlerTests.cs
    │       │   └── Create[Entidade]CommandValidatorTests.cs
    │       ├── Delete[Entidade]/
    │       └── Update[Entidade]/
    │── Queries/
    │    └── [Entidade]Queries/
    │       ├── GetAll[Entidades]/
    │       │   └── GetAll[Entidades]QueryHandlerTests.cs
    │       └── Get[Entidade]ById/
    │           ├── Get[Entidade]ByIdQueryHandlerTests.cs
    │           └── Get[Entidade]ByIdQueryValidatorTests.cs
    │── Domain/
    │   └── [Entidade]Tests.cs
    │── Services/
    │   └── [Service]Tests.cs    
    └── Repositories/
        └── [Entidade]RepositoryTests.cs
    
        
```

---

## Estrutura da IntegrationEvents

```
IntegrationEvents/
└── Events/
    └── [Entidade]ChangedIntegrationEvent.cs
```

---

## Persistência: Repositórios e Unit of Work

Utilizamos o padrão **Repository** para abstração do acesso a dados e o padrão **Unit of Work** para garantir atomicidade e controle transacional das operações de escrita.

### Declaração do Unit of Work na Application

```csharp
private readonly IUnitOfWork<[Modulo]DbContext> _unitOfWork;
```

### ICurrentUser para Auditoria

Para operações de **Update** e **Delete**, utilizar o `ICurrentUser` para informar o usuário responsável pela alteração/exclusão:

```csharp
private readonly ICurrentUser _currentUser;
```

---

## Decisões de Design

| Decisão | Descrição |
|---------|-----------|
| **Sem Exceptions** | Não utilizamos exceptions para regras de negócio |
| **Encapsulamento** | Propriedades com `private set` |
| **Métodos Controlados** | Alterações via métodos públicos |
| **Unit of Work** | Controle transacional obrigatório |
| **BaseEntidadeMap** | Todos os maps devem estender de `BaseEntidadeMap<T>` |

---

## Commands (Operações de Escrita)

### [Entidade]

#### Create[Entidade]Command
- **Finalidade:** [Descrição]
- **Localização:** `Commands/[Entidade]Commands/Create[Entidade]/`
- **Retorno:** `OperationResult<Guid>`
- **Endpoint:** `POST /v1/[entidades]`
- **Dependências:** `IUnitOfWork<[Modulo]DbContext>`

**Validações:**
- [Lista de validações]

**Exemplo de payload:**
```json
{
  "campo1": "valor1",
  "campo2": "valor2"
}
```

---

## Queries (Operações de Leitura)

### [Entidade]

#### GetAll[Entidades]Query
- **Finalidade:** [Descrição]
- **Localização:** `Queries/[Entidade]Queries/GetAll[Entidades]/`
- **Retorno:** `OperationResult<List<GetAll[Entidades]Response>>`
- **Endpoint:** `GET /v1/[entidades]`
- **Regras:** [Lista de regras]

---

## Observações Importantes

| Item | Descrição |
|------|-----------|
| **Soft Delete** | Exclusão apenas marca como excluído, não remove do banco |
| **Campo Ativo** | Herdado de `BaseEntidade` |

---

# FUNCIONALIDADES TÉCNICAS (Esta Opção é opcional, deve ser validada caso precise).

## Sistema de Cache Redis

O módulo utiliza cache Redis para otimizar consultas frequentes e melhorar a performance da aplicação.

### Configuração

O cache Redis é configurado automaticamente através do serviço compartilhado `ICacheService` localizado no módulo `Az.Shared.Shared`.

```csharp
// Configuração no módulo
// InstanceName vazio para evitar duplicação de prefixo
services.AddRedisCache(configuration, "");
```

### Estrutura de Chaves

As chaves de cache seguem um padrão hierárquico:
```
azfood:nomeModulo:{entidade}:{operacao}:{parametros}
```

**Exemplos:**
- `azfood:nomeModulo:cardapio:all`

### Invalidação Automática

O cache é invalidado automaticamente nas seguintes operações:
- **Create:** 
- **Update:** 
- **Delete:** 

## Migrations

Para executar as migrations, utilize o seguinte comando:
```bash
dotnet ef migrations add [NomeDaMigration] --project Az.[NomeDoModulo].Infrastructure --startup-project src/Api/Az.Api --context [NomeDoDbContext]
```

## Diretrizes para Futuras Documentações

### ❌ O que NÃO fazer

- ❌ Inserir funcionalidades técnicas no meio da documentação de endpoints
- ❌ Misturar configurações com documentação de API
- ❌ Adicionar seções sem seguir a estrutura definida
- ❌ Documentar funcionalidades técnicas junto com regras de negócio

### ✅ O que fazer

- ✅ Seguir a estrutura definida rigorosamente
- ✅ Adicionar funcionalidades técnicas na seção dedicada
- ✅ Manter separação clara entre API e funcionalidades técnicas
- ✅ Usar a seção "Observações Importantes" para informações gerais
